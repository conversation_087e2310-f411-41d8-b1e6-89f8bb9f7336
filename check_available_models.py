#!/usr/bin/env python3
"""
Скрипт для проверки всех доступных моделей в Gemini API.
"""

import sys
import os
from datetime import datetime

# Добавляем текущую директорию в путь для импорта модулей
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_test(message, level="INFO"):
    """Логирование с временной меткой."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def check_all_models():
    """Проверяет все доступные модели."""
    log_test("🔍 Проверка всех доступных моделей...")
    
    try:
        import config
        from google import genai
        
        if not config.OFFICIAL_GEMINI_API_KEYS:
            log_test("❌ Список API ключей пуст", "ERROR")
            return False
            
        # Тестируем первый ключ
        test_key = config.OFFICIAL_GEMINI_API_KEYS[0]
        log_test(f"🔑 Тестирование ключа ...{test_key[-4:]}")
        
        client = genai.Client(api_key=test_key)
        
        # Получаем список всех моделей
        try:
            models = list(client.models.list())
            log_test(f"✅ API ключ работает, доступно {len(models)} моделей")
            
            # Ищем модели Gemini 2.0
            gemini_2_models = []
            image_models = []
            
            for model in models:
                model_name = model.name
                if "gemini-2" in model_name.lower():
                    gemini_2_models.append(model_name)
                if "image" in model_name.lower() or "imagen" in model_name.lower():
                    image_models.append(model_name)
            
            log_test("📋 Все доступные модели:")
            for model in models:
                log_test(f"   - {model.name}")
            
            log_test("\n🎯 Модели Gemini 2.0:")
            if gemini_2_models:
                for model in gemini_2_models:
                    log_test(f"   ✅ {model}")
            else:
                log_test("   ❌ Нет доступных моделей Gemini 2.0")
            
            log_test("\n🎨 Модели для работы с изображениями:")
            if image_models:
                for model in image_models:
                    log_test(f"   ✅ {model}")
            else:
                log_test("   ❌ Нет доступных моделей для изображений")
            
            # Проверяем конкретные модели
            target_models = [
                "gemini-2.0-flash-preview-image-generation",
                "gemini-2.0-flash",
                "gemini-2.0-flash-exp",
                "imagen-3.0-generate-002",
                "imagen-4.0-generate-preview-06-06"
            ]
            
            log_test("\n🔍 Проверка целевых моделей:")
            for target in target_models:
                found = any(target in model.name for model in models)
                status = "✅" if found else "❌"
                log_test(f"   {status} {target}")
            
            return True
            
        except Exception as e:
            log_test(f"❌ Ошибка при получении списка моделей: {e}", "ERROR")
            return False
            
    except Exception as e:
        log_test(f"❌ Ошибка при проверке моделей: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Основная функция."""
    log_test("🚀 Запуск проверки доступных моделей")
    log_test("=" * 60)
    
    check_all_models()
    
    log_test("=" * 60)
    log_test("🏁 Проверка завершена")

if __name__ == "__main__":
    main()
