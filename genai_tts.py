"""
Модуль для работы с TTS функциями через официальную библиотеку Google GenAI.
Миграция TTS функций с сохранением всех настроек и конфигураций.
"""

import random
import time
import struct
import mimetypes
from typing import Optional
from google import genai
from google.genai import types, errors
from bot_globals import log_admin
import config
from genai_client import create_safety_settings

# Константы для обработки "тихих" ключей
MAX_TTS_KEY_ATTEMPTS = 6  # максимум 6 «тихих» ключей подряд
SILENT_CHUNK_TIMEOUT = 20  # сек. ждём inline_data


def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Generates a WAV file header for the given audio data and parameters.

    Args:
        audio_data: The raw audio data as a bytes object.
        mime_type: Mime type of the audio data.

    Returns:
        A bytes object representing the WAV file header.
    """
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

    # http://soundfile.sapp.org/doc/WaveFormat/

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",          # ChunkID
        chunk_size,       # ChunkSize (total file size - 8 bytes)
        b"WAVE",          # Format
        b"fmt ",          # Subchunk1ID
        16,               # Subchunk1Size (16 for PCM)
        1,                # AudioFormat (1 for PCM)
        num_channels,     # NumChannels
        sample_rate,      # SampleRate
        byte_rate,        # ByteRate
        block_align,      # BlockAlign
        bits_per_sample,  # BitsPerSample
        b"data",          # Subchunk2ID
        data_size         # Subchunk2Size (size of audio data)
    )
    return header + audio_data


def parse_audio_mime_type(mime_type: str) -> dict[str, int | None]:
    """Parses bits per sample and rate from an audio MIME type string.

    Assumes bits per sample is encoded like "L16" and rate as "rate=xxxxx".

    Args:
        mime_type: The audio MIME type string (e.g., "audio/L16;rate=24000").

    Returns:
        A dictionary with "bits_per_sample" and "rate" keys. Values will be
        integers if found, otherwise None.
    """
    bits_per_sample = 16
    rate = 24000

    # Extract rate from parameters
    parts = mime_type.split(";")
    for param in parts: # Skip the main type part
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate_str = param.split("=", 1)[1]
                rate = int(rate_str)
            except (ValueError, IndexError):
                # Handle cases like "rate=" with no value or non-integer value
                pass # Keep rate as default
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L", 1)[1])
            except (ValueError, IndexError):
                pass # Keep bits_per_sample as default if conversion fails

    return {"bits_per_sample": bits_per_sample, "rate": rate}


def call_gemini_tts_api_genai(dialogue_text, call_type="podcast_tts", podcast_host_type="diana_sasha"):
    """
    Миграция call_gemini_tts_api на официальную библиотеку genai.
    Calls Gemini 2.5 Flash TTS API to generate podcast audio using native multi-speaker mode.
    Returns audio data as bytes or None on error.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiTTS-GenAI] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API key not found")
        return None

    if not dialogue_text or not isinstance(dialogue_text, str):
        log_admin(f"{log_prefix}Invalid dialogue text provided")
        return None

    # Validate dialogue format
    if "Woman:" not in dialogue_text or "Man:" not in dialogue_text:
        log_admin(f"{log_prefix}Dialogue text doesn't contain required speaker markers (Woman:/Man:)")
        return None

    # Add prefix instruction before dialogue
    tts_prefix = "Диалог между пьяной в хлам парочкой. Они еле говорят, особенно заплетается язык у женщины: она флиртует, проститутка, и очень пьяная. Не повторяйте реплики!!!!!!!\n\n"
    full_tts_text = tts_prefix + dialogue_text

    # Select voices based on podcast host type
    if podcast_host_type == "anna_mikhail":
        woman_voice = config.PODCAST_ANNA_VOICE
        man_voice = config.PODCAST_MIKHAIL_VOICE
        log_admin(f"{log_prefix}Using Anna & Mikhail voices: {woman_voice}, {man_voice}")
    else:
        woman_voice = config.PODCAST_WOMAN_VOICE
        man_voice = config.PODCAST_MAN_VOICE
        log_admin(f"{log_prefix}Using Diana & Sasha voices: {woman_voice}, {man_voice}")

    log_admin(f"{log_prefix}Generating multi-speaker audio for dialogue ({len(full_tts_text)} characters with prefix)")

    # Shuffle keys for random selection
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    tts_model = "gemini-2.5-flash-preview-tts"

    # Try each key until success or all keys exhausted
    for attempt, api_key in enumerate(available_keys, 1):
        log_admin(f"{log_prefix}Attempting TTS with key ending ...{api_key[-4:]} (attempt {attempt}/{len(available_keys)})")

        try:
            log_admin(f"{log_prefix}Initializing Gemini client...")
            client = genai.Client(
                api_key=api_key,
            )

            # Prepare contents exactly as in working example
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part(text=full_tts_text),
                    ],
                ),
            ]

            # Multi-speaker configuration exactly as in working example
            generate_content_config = types.GenerateContentConfig(
                temperature=1,
                response_modalities=[
                    "audio",
                ],
                speech_config=types.SpeechConfig(
                    multi_speaker_voice_config=types.MultiSpeakerVoiceConfig(
                        speaker_voice_configs=[
                            types.SpeakerVoiceConfig(
                                speaker="Woman",
                                voice_config=types.VoiceConfig(
                                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                                        voice_name=woman_voice
                                    )
                                ),
                            ),
                            types.SpeakerVoiceConfig(
                                speaker="Man",
                                voice_config=types.VoiceConfig(
                                    prebuilt_voice_config=types.PrebuiltVoiceConfig(
                                        voice_name=man_voice
                                    )
                                ),
                            ),
                        ]
                    ),
                ),
            )

            file_index = 0
            audio_found = False
            log_admin(f"{log_prefix}Starting content generation...")

            # Use streaming exactly as in working example
            try:
                start_t = time.monotonic()
                for chunk in client.models.generate_content_stream(
                    model=tts_model,
                    contents=contents,
                    config=generate_content_config,
                ):
                    # log_admin(f"{log_prefix}Received chunk: {chunk}")  # Disabled verbose logging
                    if (
                        chunk.candidates is None
                        or chunk.candidates[0].content is None
                        or chunk.candidates[0].content.parts is None
                    ):
                        # log_admin(f"{log_prefix}Skipping chunk with no content")  # Disabled verbose logging
                        continue
                    if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                        audio_found = True
                        # log_admin(f"{log_prefix}Found audio data in chunk {file_index}")  # Disabled verbose logging
                        file_index += 1
                        inline_data = chunk.candidates[0].content.parts[0].inline_data
                        data_buffer = inline_data.data
                        file_extension = mimetypes.guess_extension(inline_data.mime_type)
                        if file_extension is None:
                            file_extension = ".wav"
                            data_buffer = convert_to_wav(inline_data.data, inline_data.mime_type)

                        # Return the first audio chunk immediately (as in working example)
                        log_admin(f"{log_prefix}Successfully generated audio ({len(data_buffer)} bytes)")
                        return data_buffer
                    else:
                        log_admin(f"{log_prefix}Text chunk: {chunk.text}")

                # If we get here, no audio data was found
                if not audio_found:
                    log_admin(f"{log_prefix}Key ...{api_key[-4:]} был «тихий», пробуем следующий", level="warning")
                    continue  # ⚠️ идём на следующий ключ

            except Exception as e:
                log_admin(f"{log_prefix}Error during generation: {e}")
                import traceback
                traceback.print_exc()
                continue

        except errors.APIError as e:
            log_admin(f"{log_prefix}API Error with key ...{api_key[-4:]}: Code {e.code}, Message: {e.message}")
            # Don't retry on certain error codes
            if e.code in [400, 401, 403]:  # Bad request, unauthorized, forbidden
                log_admin(f"{log_prefix}Non-retryable error code {e.code}, skipping to next key")
                continue
            # For other API errors, wait a bit before trying next key
            time.sleep(2)
            continue
        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error with key ...{api_key[-4:]}: {type(e).__name__}: {e}")
            continue

    # После цикла, если ни один ключ не дал звук
    raise RuntimeError("TTS_FAILED_NO_AUDIO")


def call_gemini_single_tts_api_genai(text, call_type="simple_tts", voice_name="Zephyr"):
    """
    Миграция call_gemini_single_tts_api на официальную библиотеку genai.
    Calls Gemini TTS API for single speaker text-to-speech.
    Returns audio data as bytes or None on error.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiTTS-GenAI] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API key not found")
        return None

    if not text or not isinstance(text, str):
        log_admin(f"{log_prefix}Invalid text provided")
        return None

    log_admin(f"{log_prefix}Generating single speaker audio for text ({len(text)} characters)")

    # Shuffle keys for random selection
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    tts_model = "gemini-2.5-flash-preview-tts"

    # Try each key until success or all keys exhausted
    for attempt, api_key in enumerate(available_keys):
        log_admin(f"{log_prefix}Attempting single TTS with key ending ...{api_key[-4:]} (attempt {attempt + 1}/{len(available_keys)})")

        try:
            log_admin(f"{log_prefix}Initializing Gemini client...")
            client = genai.Client(
                api_key=api_key,
            )

            # Prepare contents as in the example
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part(text=text),
                    ],
                ),
            ]

            # Single speaker configuration with temperature as in example
            generation_config = types.GenerateContentConfig(
                temperature=1,
                response_modalities=["audio"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(
                            voice_name=voice_name
                        )
                    )
                )
            )

            file_index = 0
            log_admin(f"{log_prefix}Starting single speaker content generation...")

            # Use streaming exactly as in working example
            try:
                for chunk in client.models.generate_content_stream(
                    model=tts_model,
                    contents=contents,
                    config=generation_config,
                ):
                    # log_admin(f"{log_prefix}Received chunk: {chunk}")  # Disabled verbose logging
                    if (
                        chunk.candidates is None
                        or chunk.candidates[0].content is None
                        or chunk.candidates[0].content.parts is None
                    ):
                        # log_admin(f"{log_prefix}Skipping chunk with no content")  # Disabled verbose logging
                        continue
                    if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                        # log_admin(f"{log_prefix}Found audio data in chunk {file_index}")  # Disabled verbose logging
                        file_index += 1
                        inline_data = chunk.candidates[0].content.parts[0].inline_data
                        data_buffer = inline_data.data
                        file_extension = mimetypes.guess_extension(inline_data.mime_type)
                        if file_extension is None:
                            file_extension = ".wav"
                            data_buffer = convert_to_wav(inline_data.data, inline_data.mime_type)

                        # Return the first audio chunk immediately (as in working example)
                        log_admin(f"{log_prefix}Successfully generated single speaker audio ({len(data_buffer)} bytes)")
                        return data_buffer
                    else:
                        log_admin(f"{log_prefix}Text chunk: {chunk.text}")

                # If we get here, no audio data was found
                log_admin(f"{log_prefix}No audio data found in single speaker response with key ...{api_key[-4:]}")
                continue

            except Exception as e:
                log_admin(f"{log_prefix}Error during single speaker generation: {e}")
                import traceback
                traceback.print_exc()
                continue

        except errors.APIError as e:
            log_admin(f"{log_prefix}API Error with key ...{api_key[-4:]}: Code {e.code}, Message: {e.message}")
            # Don't retry on certain error codes
            if e.code in [400, 401, 403]:  # Bad request, unauthorized, forbidden
                log_admin(f"{log_prefix}Non-retryable error code {e.code}, skipping to next key")
                continue
            # For other API errors, wait a bit before trying next key
            time.sleep(2)
            continue
        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error with key ...{api_key[-4:]}: {type(e).__name__}: {e}")
            continue

    # All Gemini keys failed - try fallback to VoidAI TTS
    log_admin(f"{log_prefix}All {len(available_keys)} Gemini API keys failed for single TTS generation")
    log_admin(f"{log_prefix}Attempting fallback to VoidAI TTS")

    try:
        from api_clients import call_gemini_single_tts_api_via_voidai
        fallback_audio = call_gemini_single_tts_api_via_voidai(text, call_type, voice_name)
        if fallback_audio:
            log_admin(f"{log_prefix}Fallback to VoidAI TTS successful")
            return fallback_audio
        else:
            log_admin(f"{log_prefix}Fallback to VoidAI TTS also failed")
    except Exception as e:
        log_admin(f"{log_prefix}Error during VoidAI fallback: {e}")

    return None
