"""
Глобальный менеджер пула потоков для оптимизации памяти бота.
Заменяет множественные локальные ThreadPoolExecutor на единый глобальный пул.
"""

import threading
import time
import weakref
import gc
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Callable, Any, Optional, Dict, Set
from bot_globals import log_admin


class ThreadPoolManager:
    """
    Глобальный менеджер пула потоков с автоматической очисткой и мониторингом.
    """
    
    def __init__(self, max_workers: int = 16):
        """
        Инициализация менеджера пула потоков.
        
        Args:
            max_workers: Максимальное количество рабочих потоков
        """
        self.max_workers = max_workers
        self._executor: Optional[ThreadPoolExecutor] = None
        self._lock = threading.RLock()
        self._active_futures: Set[Future] = set()
        self._futures_lock = threading.Lock()
        self._cleanup_interval = 30  # Более агрессивная очистка каждые 30 секунд
        self._last_cleanup = time.time()
        self._shutdown = False
        
        # Статистика
        self._total_submitted = 0
        self._total_completed = 0
        self._total_failed = 0
        
        log_admin(f"ThreadPoolManager initialized with max_workers={max_workers}", level="info")
    
    @property
    def executor(self) -> ThreadPoolExecutor:
        """Ленивая инициализация ThreadPoolExecutor."""
        if self._executor is None or self._executor._shutdown:
            with self._lock:
                if self._executor is None or self._executor._shutdown:
                    if self._executor is not None:
                        log_admin("Recreating ThreadPoolExecutor after shutdown", level="warning")
                    
                    self._executor = ThreadPoolExecutor(
                        max_workers=self.max_workers,
                        thread_name_prefix="GlobalPool"
                    )
                    log_admin(f"ThreadPoolExecutor created with {self.max_workers} workers", level="info")
        
        return self._executor
    
    def submit(self, fn: Callable, *args, **kwargs) -> Future:
        """
        Отправить задачу в пул потоков с автоматической очисткой.
        
        Args:
            fn: Функция для выполнения
            *args: Позиционные аргументы
            **kwargs: Именованные аргументы
            
        Returns:
            Future объект для отслеживания выполнения
        """
        if self._shutdown:
            raise RuntimeError("ThreadPoolManager is shutdown")
        
        # Автоматическая очистка завершенных задач
        self._cleanup_completed_futures()

        # Принудительная сборка мусора при превышении 20 активных задач
        with self._futures_lock:
            if len(self._active_futures) > 20:
                log_admin(f"Too many active futures ({len(self._active_futures)}), forcing cleanup and GC", level="warning")
                self.force_cleanup()

        future = self.executor.submit(fn, *args, **kwargs)

        # Добавляем future в отслеживаемые
        with self._futures_lock:
            self._active_futures.add(future)
            self._total_submitted += 1
        
        # Добавляем callback для автоматической очистки
        def cleanup_callback(fut):
            with self._futures_lock:
                self._active_futures.discard(fut)
                if fut.exception():
                    self._total_failed += 1
                    log_admin(f"Task failed: {fut.exception()}", level="error")
                else:
                    self._total_completed += 1
        
        future.add_done_callback(cleanup_callback)
        
        return future
    
    def submit_with_timeout(self, fn: Callable, timeout: float, *args, **kwargs) -> Future:
        """
        Отправить задачу с таймаутом.
        
        Args:
            fn: Функция для выполнения
            timeout: Максимальное время выполнения в секундах
            *args: Позиционные аргументы
            **kwargs: Именованные аргументы
            
        Returns:
            Future объект
        """
        def wrapper():
            # Используем threading.Timer вместо signal для кроссплатформенности
            # и избежания ошибки "signal only works in main thread"
            import threading

            result_container = [None]
            exception_container = [None]
            completed = threading.Event()

            def target():
                try:
                    result_container[0] = fn(*args, **kwargs)
                except Exception as e:
                    exception_container[0] = e
                finally:
                    completed.set()

            # Запускаем задачу в отдельном потоке
            task_thread = threading.Thread(target=target, daemon=True)
            task_thread.start()

            # Ждем завершения с таймаутом
            if completed.wait(timeout=timeout):
                # Задача завершилась вовремя
                if exception_container[0]:
                    raise exception_container[0]
                return result_container[0]
            else:
                # Таймаут - задача все еще выполняется
                raise TimeoutError(f"Task timed out after {timeout} seconds")
        
        return self.submit(wrapper)
    
    def map(self, fn: Callable, iterable, timeout: Optional[float] = None):
        """
        Применить функцию к каждому элементу итерируемого объекта параллельно.
        
        Args:
            fn: Функция для применения
            iterable: Итерируемый объект
            timeout: Максимальное время ожидания
            
        Yields:
            Результаты выполнения функции
        """
        futures = [self.submit(fn, item) for item in iterable]
        
        try:
            for future in as_completed(futures, timeout=timeout):
                yield future.result()
        except Exception as e:
            # Отменяем все незавершенные задачи
            for future in futures:
                future.cancel()
            raise e
    
    def _cleanup_completed_futures(self):
        """Очистка завершенных futures для освобождения памяти."""
        current_time = time.time()
        
        # Очистка только если прошло достаточно времени
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        with self._futures_lock:
            completed_futures = [f for f in self._active_futures if f.done()]
            for future in completed_futures:
                self._active_futures.discard(future)
            
            if completed_futures:
                log_admin(f"Cleaned up {len(completed_futures)} completed futures", level="debug")
        
        self._last_cleanup = current_time
        
        # Принудительная сборка мусора при большом количестве очищенных задач
        if len(completed_futures) > 5:  # Более агрессивная сборка мусора
            gc.collect()
            log_admin(f"Triggered garbage collection after cleaning {len(completed_futures)} futures", level="debug")
    
    def get_stats(self) -> Dict[str, Any]:
        """Получить статистику работы пула потоков."""
        with self._futures_lock:
            active_count = len(self._active_futures)
        
        return {
            "max_workers": self.max_workers,
            "active_futures": active_count,
            "total_submitted": self._total_submitted,
            "total_completed": self._total_completed,
            "total_failed": self._total_failed,
            "executor_shutdown": self._executor is None or self._executor._shutdown if self._executor else True
        }
    
    def force_cleanup(self):
        """Принудительная очистка всех завершенных задач."""
        with self._futures_lock:
            completed_futures = [f for f in self._active_futures if f.done()]
            for future in completed_futures:
                self._active_futures.discard(future)
            
            log_admin(f"Force cleanup: removed {len(completed_futures)} completed futures", level="info")
        
        # Принудительная сборка мусора
        gc.collect()
        self._last_cleanup = time.time()
    
    def shutdown(self, wait: bool = True):
        """
        Завершить работу пула потоков.
        
        Args:
            wait: Ждать завершения всех задач
        """
        self._shutdown = True
        
        if self._executor is not None:
            log_admin("Shutting down ThreadPoolExecutor", level="info")
            self._executor.shutdown(wait=wait)
            
        # Очистка всех futures
        with self._futures_lock:
            self._active_futures.clear()
        
        log_admin("ThreadPoolManager shutdown complete", level="info")
    
    def __del__(self):
        """Деструктор для корректного завершения работы."""
        if not self._shutdown:
            self.shutdown(wait=False)


# Глобальный экземпляр менеджера пула потоков
thread_pool_manager = ThreadPoolManager(max_workers=16)


def submit_task(fn: Callable, *args, **kwargs) -> Future:
    """
    Удобная функция для отправки задачи в глобальный пул.
    
    Args:
        fn: Функция для выполнения
        *args: Позиционные аргументы
        **kwargs: Именованные аргументы
        
    Returns:
        Future объект
    """
    return thread_pool_manager.submit(fn, *args, **kwargs)


def submit_task_with_timeout(fn: Callable, timeout: float, *args, **kwargs) -> Future:
    """
    Удобная функция для отправки задачи с таймаутом.
    
    Args:
        fn: Функция для выполнения
        timeout: Максимальное время выполнения
        *args: Позиционные аргументы
        **kwargs: Именованные аргументы
        
    Returns:
        Future объект
    """
    return thread_pool_manager.submit_with_timeout(fn, timeout, *args, **kwargs)


def get_thread_pool_stats() -> Dict[str, Any]:
    """Получить статистику глобального пула потоков."""
    return thread_pool_manager.get_stats()


def cleanup_thread_pool():
    """Принудительная очистка глобального пула потоков."""
    thread_pool_manager.force_cleanup()
