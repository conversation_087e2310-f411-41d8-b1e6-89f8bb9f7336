"""
Модуль для работы с официальной библиотекой Google GenAI.
Заменяет прямые HTTP вызовы к Gemini API на использование официального SDK.
Поддерживает прокси для обработки ошибок 429 (rate limit).
Оптимизирован для многопользовательского использования с мониторингом производительности.
"""

import random
import asyncio
import base64
import threading
import time
import uuid
from typing import List, Dict, Any, Optional, Union, Callable
from google import genai
from google.genai import types
from bot_globals import log_admin
import config
from performance_monitor import performance_monitor
from resource_manager import resource_manager
from retry_utils import retry_on_censorship, retry_on_censorship_async


class GenAIClientManager:
    """
    Менеджер для работы с множественными API ключами Google GenAI с поддержкой прокси.
    Оптимизирован для многопользовательского использования с мониторингом производительности.
    """

    def __init__(self):
        self.api_keys = config.OFFICIAL_GEMINI_API_KEYS
        self._clients = {}
        self._async_clients = {}
        self._key_states = {}  # Отслеживание состояния ключей: 'normal', 'rate_limited'
        self._lock = threading.RLock()  # Для thread-safe операций

        # Настройки производительности
        self._connection_timeout = getattr(config, 'GENAI_CONNECTION_TIMEOUT', 30)
        self._read_timeout = getattr(config, 'GENAI_READ_TIMEOUT', 300)
        self._max_retries = getattr(config, 'GENAI_MAX_RETRIES', 3)
        self._retry_delay = getattr(config, 'GENAI_RETRY_DELAY', 1.0)
        self._retry_backoff = getattr(config, 'GENAI_RETRY_BACKOFF_FACTOR', 2.0)
        self._retry_max_delay = getattr(config, 'GENAI_RETRY_MAX_DELAY', 60.0)

        log_admin(f"[GenAIClientManager] Initialized with {len(self.api_keys)} API keys and performance optimizations", level="info")
    
    def get_client(self, api_key: str = None) -> genai.Client:
        """Получить синхронный клиент для указанного API ключа."""
        if api_key is None:
            api_key = random.choice(self.api_keys)

        with self._lock:
            if api_key not in self._clients:
                # Создаем HTTP опции без timeout параметра для избежания конфликтов
                http_options = types.HttpOptions()

                self._clients[api_key] = genai.Client(
                    api_key=api_key,
                    http_options=http_options
                )

                # Регистрируем клиента в менеджере ресурсов
                client_id = f"sync_{api_key[-4:]}_{uuid.uuid4().hex[:8]}"
                resource_manager.register_client(client_id, api_key, is_async=False)

                log_admin(f"[GenAI] Created optimized sync client for key ...{api_key[-4:] if api_key else '****'}", level="debug")

        return self._clients[api_key]
    
    def get_async_client(self, api_key: str = None) -> genai.Client:
        """Получить асинхронный клиент для указанного API ключа."""
        if api_key is None:
            api_key = random.choice(self.api_keys)

        with self._lock:
            if api_key not in self._async_clients:
                # Создаем HTTP опции без timeout параметра для избежания конфликтов
                http_options = types.HttpOptions()

                self._async_clients[api_key] = genai.Client(
                    api_key=api_key,
                    http_options=http_options
                )

                # Регистрируем клиента в менеджере ресурсов
                client_id = f"async_{api_key[-4:]}_{uuid.uuid4().hex[:8]}"
                resource_manager.register_client(client_id, api_key, is_async=True)

                log_admin(f"[GenAI] Created optimized async client for key ...{api_key[-4:] if api_key else '****'}", level="debug")

        return self._async_clients[api_key]
    


    def get_shuffled_keys(self) -> List[str]:
        """Получить перемешанный список API ключей для fallback."""
        keys = list(self.api_keys)
        random.shuffle(keys)
        return keys



    def get_client_stats(self) -> Dict[str, int]:
        """Получить статистику по количеству клиентов в кэше."""
        with self._lock:
            return {
                'normal_clients': len(self._clients),
                'async_clients': len(self._async_clients),
                'total_api_keys': len(self.api_keys)
            }

    def get_key_state(self, api_key: str) -> str:
        """Получить состояние API ключа."""
        with self._lock:
            return self._key_states.get(api_key, 'normal')

    def set_key_state(self, api_key: str, state: str) -> None:
        """Установить состояние API ключа."""
        with self._lock:
            self._key_states[api_key] = state
            log_admin(f"[GenAI] Key ...{api_key[-4:] if api_key else '****'} state changed to: {state}", level="debug")

    def reset_key_states(self) -> None:
        """Сбросить состояния всех ключей."""
        with self._lock:
            self._key_states.clear()
            log_admin("[GenAI] All key states reset to normal", level="info")

    def get_ordered_keys(self) -> List[str]:
        """Получить ключи в оптимальном порядке: сначала нормальные, потом с ограничениями."""
        with self._lock:
            keys = list(self.api_keys)
            random.shuffle(keys)  # Перемешиваем для равномерного распределения нагрузки

            # Сортируем по состоянию: normal ключи первыми
            normal_keys = [k for k in keys if self.get_key_state(k) == 'normal']
            rate_limited_keys = [k for k in keys if self.get_key_state(k) == 'rate_limited']

            return normal_keys + rate_limited_keys

    def get_key_states_info(self) -> Dict[str, Dict[str, int]]:
        """Получить информацию о состояниях ключей."""
        with self._lock:
            states_count = {'normal': 0, 'rate_limited': 0}
            for key in self.api_keys:
                state = self.get_key_state(key)
                states_count[state] = states_count.get(state, 0) + 1

            return {
                'states_count': states_count,
                'total_keys': len(self.api_keys)
            }

    def warmup_connections(self, max_keys: int = 3) -> Dict[str, bool]:
        """
        Предварительный "разогрев" соединений для быстрого отклика после простоя.
        
        Args:
            max_keys: Максимальное количество ключей для разогрева
            
        Returns:
            Словарь с результатами разогрева для каждого ключа
        """
        log_admin(f"[GenAI] Starting connection warmup for {max_keys} keys", level="info")
        
        keys_to_warmup = self.get_ordered_keys()[:max_keys]
        warmup_results = {}
        
        for api_key in keys_to_warmup:
            key_suffix = api_key[-4:] if isinstance(api_key, str) and len(api_key) >= 4 else "****"
            
            try:
                # Создаем клиента (это инициализирует соединение)
                client = self.get_client(api_key)
                
                # Простой тестовый запрос для проверки соединения
                test_config = create_generation_config(
                    max_output_tokens=10,
                    temperature=0.1
                )
                
                start_time = time.time()
                
                # Минимальный запрос для проверки соединения
                response = client.models.generate_content(
                    model=config.MODEL_GEMINI_2_5_FLASH,
                    contents=[types.UserContent(parts=[types.Part(text="Hi")])],
                    config=test_config
                )
                
                warmup_time = time.time() - start_time
                warmup_results[api_key] = True
                
                log_admin(f"[GenAI] Warmup successful for key ...{key_suffix} ({warmup_time:.2f}s)", level="debug")
                
            except Exception as e:
                warmup_results[api_key] = False
                error_msg = str(e)[:100]
                log_admin(f"[GenAI] Warmup failed for key ...{key_suffix}: {error_msg}", level="warning")
                
                # Если это rate limit, отмечаем ключ
                if is_rate_limit_error(e):
                    self.set_key_state(api_key, 'rate_limited')
        
        successful_warmups = sum(1 for success in warmup_results.values() if success)
        log_admin(f"[GenAI] Connection warmup completed: {successful_warmups}/{len(keys_to_warmup)} successful", level="info")
        
        return warmup_results

    def cleanup_unused_clients(self, force: bool = False) -> Dict[str, int]:
        """
        ЭТАП 3: Автоматическая очистка неиспользуемых клиентов.

        Args:
            force: Принудительная очистка всех клиентов

        Returns:
            Статистика очистки
        """
        import gc

        with self._lock:
            stats = {
                'normal_clients_before': len(self._clients),
                'async_clients_before': len(self._async_clients)
            }

            if force:
                # Принудительная очистка всех клиентов
                self._clients.clear()
                self._async_clients.clear()
                log_admin("[GenAI] Force cleanup: All clients cleared", level="info")
            else:
                # Ограничиваем количество обычных клиентов
                max_clients = 5  # Максимум 5 клиентов каждого типа

                if len(self._clients) > max_clients:
                    # Оставляем только последние max_clients клиентов
                    keys_to_keep = list(self._clients.keys())[-max_clients:]
                    new_clients = {k: self._clients[k] for k in keys_to_keep}
                    removed_count = len(self._clients) - len(new_clients)
                    self._clients = new_clients
                    log_admin(f"[GenAI] Removed {removed_count} old sync clients", level="debug")

                if len(self._async_clients) > max_clients:
                    # Оставляем только последние max_clients клиентов
                    keys_to_keep = list(self._async_clients.keys())[-max_clients:]
                    new_async_clients = {k: self._async_clients[k] for k in keys_to_keep}
                    removed_count = len(self._async_clients) - len(new_async_clients)
                    self._async_clients = new_async_clients
                    log_admin(f"[GenAI] Removed {removed_count} old async clients", level="debug")

            stats.update({
                'normal_clients_after': len(self._clients),
                'async_clients_after': len(self._async_clients)
            })

            # Подсчитываем общее количество очищенных клиентов
            total_cleaned = (
                (stats['normal_clients_before'] - stats['normal_clients_after']) +
                (stats['async_clients_before'] - stats['async_clients_after'])
            )

            if total_cleaned > 0:
                log_admin(f"[GenAI] Client cleanup completed: {total_cleaned} clients removed", level="info")

                # Принудительная сборка мусора после очистки клиентов
                gc.collect()

            stats['total_cleaned'] = total_cleaned
            return stats


# Глобальный экземпляр менеджера
client_manager = GenAIClientManager()

# Экстренная очистка клиентов удалена (модуль управления памятью удален)


def is_rate_limit_error(exception: Exception) -> bool:
    """
    Определяет, является ли исключение ошибкой rate limit (429).

    Args:
        exception: Исключение для проверки

    Returns:
        True если это ошибка rate limit, False иначе
    """
    # Проверяем сообщение об ошибке на наличие ключевых слов
    error_message = str(exception).lower()
    rate_limit_indicators = [
        '429', 'rate limit', 'quota exceeded', 'too many requests',
        'rate_limit_exceeded', 'quota_exceeded', 'resource_exhausted'
    ]

    for indicator in rate_limit_indicators:
        if indicator in error_message:
            return True

    # Проверяем тип исключения
    exception_type = type(exception).__name__.lower()
    if any(indicator in exception_type for indicator in ['ratelimit', 'quota', 'resource']):
        return True

    # Проверяем атрибуты исключения на наличие status code
    if hasattr(exception, 'status_code') and exception.status_code == 429:
        return True
    if hasattr(exception, 'code') and exception.code == 429:
        return True
    if hasattr(exception, 'response') and hasattr(exception.response, 'status_code'):
        if exception.response.status_code == 429:
            return True

    return False


def convert_history_to_genai_format(history: List[Dict], use_history: bool = True) -> List[types.Content]:
    """
    Конвертирует историю диалога в формат GenAI.

    Args:
        history: История диалога в текущем формате бота
        use_history: Использовать ли историю

    Returns:
        Список объектов types.Content для GenAI
    """
    if not use_history or not history:
        return []

    # Ограничиваем длину истории, чтобы не превысить лимит токенов
    MAX_HISTORY_TURNS = 20
    limited_history = history[-MAX_HISTORY_TURNS:] if len(history) > MAX_HISTORY_TURNS else history

    genai_contents = []

    for turn in limited_history:
        role = turn.get("role", "user")
        parts = turn.get("parts", [])
        
        if not parts:
            continue
        
        # Конвертируем роль
        if role == "model" or role == "assistant":
            genai_role = "model"
        else:
            genai_role = "user"
        
        # Конвертируем части
        genai_parts = []
        for part in parts:
            if "text" in part and part["text"]:
                genai_parts.append(types.Part(text=part["text"]))
            elif "inline_data" in part:
                inline_data = part["inline_data"]
                if "mime_type" in inline_data and "data" in inline_data:
                    genai_parts.append(types.Part.from_bytes(
                        data=base64.b64decode(inline_data["data"]),
                        mime_type=inline_data["mime_type"]
                    ))
        
        if genai_parts:
            if genai_role == "user":
                genai_contents.append(types.UserContent(parts=genai_parts))
            else:
                genai_contents.append(types.ModelContent(parts=genai_parts))
    
    return genai_contents


def convert_input_data_to_genai_parts(input_data: List[Dict]) -> List[types.Part]:
    """
    Конвертирует медиа данные в формат GenAI Parts.
    
    Args:
        input_data: Список медиа данных с mime_type и data
    
    Returns:
        Список объектов types.Part
    """
    if not input_data:
        return []
    
    genai_parts = []
    
    for media_data in input_data:
        # Поддерживаем оба формата: "data" и "base64_data"
        data = media_data.get("data") or media_data.get("base64_data")
        mime_type = media_data.get("mime_type")
        
        if data and mime_type:
            try:
                # Декодируем base64 данные
                binary_data = base64.b64decode(data)
                genai_parts.append(types.Part.from_bytes(
                    data=binary_data,
                    mime_type=mime_type
                ))
            except Exception as e:
                log_admin(f"[GenAI] Error converting media data: {e}", level="error")
    
    return genai_parts


def create_generation_config(
    max_output_tokens: int = 8192,
    temperature: float = 0.7,
    top_p: float = 0.9,
    top_k: int = 40,
    thinking_budget: int = None,
    system_instruction: str = None,
    safety_settings: List[types.SafetySetting] = None,
    tools: List[types.Tool] = None
) -> types.GenerateContentConfig:
    """
    Создает конфигурацию для генерации контента.

    Args:
        max_output_tokens: Максимальное количество токенов
        temperature: Температура (0.0-1.0)
        top_p: Top-p параметр
        top_k: Top-k параметр
        thinking_budget: Бюджет для размышления (если нужен)
        system_instruction: Системная инструкция
        safety_settings: Настройки безопасности
        tools: Список инструментов (например, Google Search)

    Returns:
        Объект GenerateContentConfig
    """
    config_dict = {
        "max_output_tokens": max_output_tokens,
        "temperature": temperature,
        "top_p": top_p,
        "top_k": top_k
    }

    # Добавляем thinking config если указан
    if thinking_budget is not None:
        config_dict["thinking_config"] = types.ThinkingConfig(
            thinking_budget=thinking_budget
        )

    # Добавляем системную инструкцию если указана
    if system_instruction:
        config_dict["system_instruction"] = system_instruction

    # Добавляем настройки безопасности если указаны
    if safety_settings:
        config_dict["safety_settings"] = safety_settings

    # Добавляем инструменты если указаны
    if tools:
        config_dict["tools"] = tools

    return types.GenerateContentConfig(**config_dict)


def create_safety_settings() -> List[types.SafetySetting]:
    """
    Создает настройки безопасности (ПОЛНОСТЬЮ отключает все блокировки).
    Включает все 5 категорий безопасности с BLOCK_NONE для максимального отключения цензуры.
    """
    return [
        types.SafetySetting(
            category="HARM_CATEGORY_HARASSMENT",
            threshold="BLOCK_NONE"
        ),
        types.SafetySetting(
            category="HARM_CATEGORY_HATE_SPEECH",
            threshold="BLOCK_NONE"
        ),
        types.SafetySetting(
            category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold="BLOCK_NONE"
        ),
        types.SafetySetting(
            category="HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold="BLOCK_NONE"
        ),
        types.SafetySetting(
            category="HARM_CATEGORY_CIVIC_INTEGRITY",
            threshold="BLOCK_NONE"
        )
    ]


def try_with_fallback(func, *args, **kwargs):
    """
    Оптимизированная функция с быстрым fallback по API ключам и умным переключением на прокси при 429.
    Включает мониторинг производительности и управление ресурсами.

    Args:
        func: Функция для выполнения
        *args: Позиционные аргументы
        **kwargs: Именованные аргументы

    Returns:
        Результат выполнения функции или None при ошибке
    """
    keys = client_manager.get_ordered_keys()
    last_error = None
    total_attempts = 0
    start_time = time.time()

    # Извлекаем модель из kwargs для мониторинга
    model_name = kwargs.get('model', 'unknown')
    if hasattr(args[0] if args else None, 'model'):
        model_name = getattr(args[0], 'model', model_name)

    log_admin(f"[GenAI] Starting optimized fallback with {len(keys)} keys", level="debug")

    # Ограничиваем количество ключей для первой попытки (быстрый старт)
    initial_keys = keys[:3]  # Пробуем только первые 3 ключа для быстроты
    remaining_keys = keys[3:] if len(keys) > 3 else []

    # Фаза 1: Быстрые попытки с первыми 3 ключами
    for api_key in initial_keys:
        total_attempts += 1
        key_suffix = api_key[-4:] if isinstance(api_key, str) and len(api_key) >= 4 else "****"
        request_start = time.time()

        try:
            client = client_manager.get_client(api_key)

            # Проверяем лимит одновременных запросов
            client_id = f"sync_{key_suffix}_{uuid.uuid4().hex[:8]}"
            if not resource_manager.start_request(client_id):
                log_admin(f"[GenAI] Concurrent request limit reached, skipping key ...{key_suffix}", level="warning")
                continue

            try:
                result = func(client, *args, **kwargs)
                request_duration = time.time() - request_start

                # Записываем успешную метрику
                performance_monitor.record_request(
                    duration=request_duration,
                    success=True,
                    api_key=api_key,
                    model=model_name,
                    proxy_used=False,
                    retry_count=total_attempts - 1
                )

                log_admin(f"[GenAI] Quick success with key ...{key_suffix} (attempt {total_attempts}, {request_duration:.2f}s)", level="debug")
                return result

            finally:
                resource_manager.end_request(client_id)

        except Exception as e:
            request_duration = time.time() - request_start
            last_error = e

            # Записываем неуспешную метрику
            error_type = "rate_limit" if is_rate_limit_error(e) else "other"
            performance_monitor.record_request(
                duration=request_duration,
                success=False,
                api_key=api_key,
                model=model_name,
                error_type=error_type,
                proxy_used=False,
                retry_count=total_attempts - 1
            )

            # При 429 ошибке отмечаем ключ как ограниченный
            if is_rate_limit_error(e):
                log_admin(f"[GenAI] Rate limit detected for key ...{key_suffix}", level="warning")
                client_manager.set_key_state(api_key, 'rate_limited')
            else:
                # Обычная ошибка, не rate limit - просто логируем
                error_msg = str(e)[:100]
                log_admin(f"[GenAI] Error for key ...{key_suffix}: {error_msg}", level="debug")

    # Фаза 2: Если быстрые попытки не сработали, пробуем остальные ключи
    if remaining_keys:
        log_admin(f"[GenAI] Quick attempts failed, trying remaining {len(remaining_keys)} keys", level="debug")
        
        for api_key in remaining_keys:
            total_attempts += 1
            key_suffix = api_key[-4:] if isinstance(api_key, str) and len(api_key) >= 4 else "****"
            request_start = time.time()

            try:
                client = client_manager.get_client(api_key)
                client_id = f"sync_{key_suffix}_{uuid.uuid4().hex[:8]}"
                
                if not resource_manager.start_request(client_id):
                    continue

                try:
                    result = func(client, *args, **kwargs)
                    request_duration = time.time() - request_start

                    performance_monitor.record_request(
                        duration=request_duration,
                        success=True,
                        api_key=api_key,
                        model=model_name,
                        proxy_used=False,
                        retry_count=total_attempts - 1
                    )

                    log_admin(f"[GenAI] Fallback success with key ...{key_suffix} (attempt {total_attempts}, {request_duration:.2f}s)", level="debug")
                    return result

                finally:
                    resource_manager.end_request(client_id)

            except Exception as e:
                request_duration = time.time() - request_start
                last_error = e

                performance_monitor.record_request(
                    duration=request_duration,
                    success=False,
                    api_key=api_key,
                    model=model_name,
                    error_type="rate_limit" if is_rate_limit_error(e) else "other",
                    proxy_used=False,
                    retry_count=total_attempts - 1
                )

                # При 429 отмечаем ключ как ограниченный
                if is_rate_limit_error(e):
                    client_manager.set_key_state(api_key, 'rate_limited')

    # Все попытки исчерпаны
    total_duration = time.time() - start_time
    try:
        error_msg = str(last_error)[:200] if last_error else "Unknown error"
        log_admin(f"[GenAI] All keys failed after {total_attempts} attempts in {total_duration:.2f}s. Last error: {error_msg}", level="error")
    except Exception:
        log_admin(f"[GenAI] All {len(keys)} API keys failed. Error logging issue.", level="error")

    return None


async def try_with_fallback_async(func, *args, **kwargs):
    """
    Асинхронная версия try_with_fallback с поддержкой автоматического переключения на прокси при 429.
    Включает мониторинг производительности и управление ресурсами.

    Args:
        func: Асинхронная функция для выполнения
        *args: Позиционные аргументы
        **kwargs: Именованные аргументы

    Returns:
        Результат выполнения функции или None при ошибке
    """
    keys = client_manager.get_ordered_keys()
    last_error = None
    total_attempts = 0
    start_time = time.time()

    # Извлекаем модель из kwargs для мониторинга
    model_name = kwargs.get('model', 'unknown')
    if hasattr(args[0] if args else None, 'model'):
        model_name = getattr(args[0], 'model', model_name)

    log_admin(f"[GenAI] Starting async fallback with {len(keys)} keys", level="debug")

    # Фаза 1: Попытки с обычными асинхронными клиентами
    for api_key in keys:
        total_attempts += 1
        key_suffix = api_key[-4:] if isinstance(api_key, str) and len(api_key) >= 4 else "****"

        try:
            client = client_manager.get_async_client(api_key)
            result = await func(client, *args, **kwargs)
            log_admin(f"[GenAI] Async success with normal client for key ...{key_suffix} (attempt {total_attempts})", level="debug")
            return result

        except Exception as e:
            last_error = e

            # Проверяем, является ли это ошибкой rate limit
            if is_rate_limit_error(e):
                log_admin(f"[GenAI] Async rate limit detected for key ...{key_suffix}", level="warning")
                client_manager.set_key_state(api_key, 'rate_limited')
            else:
                # Обычная ошибка, не rate limit
                error_msg = str(e)[:200]
                log_admin(f"[GenAI] Async normal error for key ...{key_suffix}: {error_msg}", level="warning")

    # Безопасное логирование финальной ошибки
    try:
        error_msg = str(last_error)[:200] if last_error else "Unknown error"
        log_admin(f"[GenAI] All {len(keys)} async API keys failed after {total_attempts} attempts. Last error: {error_msg}", level="error")
    except Exception:
        log_admin(f"[GenAI] All {len(keys)} async API keys failed. Error logging issue.", level="error")

    return None


# === MIGRATED FUNCTIONS ===

@retry_on_censorship(max_attempts=5)
def call_gemini_2_5_flash_api_genai(history, user_text, input_data=None, system_prompt=None, call_type="general"):
    """
    Миграция call_gemini_2_5_flash_api на официальную библиотеку genai.
    Специальная функция для вызова Gemini 2.5 Flash через официальное API для личных чатов.
    Поддерживает 65k токенов, context7, Code Thinking и веб-поиск.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemini2.5Flash-GenAI] "
    model_name = "gemini-2.5-flash"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    def _generate_content(client: genai.Client):
        # Конвертируем историю в формат GenAI
        contents = convert_history_to_genai_format(history, use_history=True)

        # Добавляем текущее сообщение пользователя
        current_parts = []
        if user_text:
            current_parts.append(types.Part(text=user_text))

        # Добавляем медиа данные
        if input_data:
            # Проверяем тип input_data
            if isinstance(input_data, list):
                media_parts = convert_input_data_to_genai_parts(input_data)
                current_parts.extend(media_parts)
            elif isinstance(input_data, dict):
                # Для одного медиа файла
                media_parts = convert_input_data_to_genai_parts([input_data])
                current_parts.extend(media_parts)

        if current_parts:
            contents.append(types.UserContent(parts=current_parts))

        # Для аудио-сводок и форматирования Perplexity, если нет user_text, но есть system_prompt, создаем пустое сообщение пользователя
        if not contents and system_prompt and call_type in ["audio_summary_gemini", "format_perplexity_output"]:
            # Создаем минимальное сообщение пользователя для запуска генерации
            if call_type == "audio_summary_gemini":
                contents.append(types.UserContent(parts=[types.Part(text="Создай сводку.")]))
            elif call_type == "format_perplexity_output":
                contents.append(types.UserContent(parts=[types.Part(text="Отформатируй ответ.")]))

        if not contents:
            # ДЕБАГ: Детальное логирование для audio_summary_gemini
            if call_type == "audio_summary_gemini":
                log_admin(
                    f"{log_prefix}DEBUG_AUDIO_NO_CONTENT - No content to process: "
                    f"user_text: '{user_text}', "
                    f"input_data: {input_data}, "
                    f"system_prompt length: {len(system_prompt) if system_prompt else 0}",
                    level="error"
                )
            return "Извините, нет данных для обработки запроса."

        # Проверяем наличие видео для отключения thinking
        has_video = False
        if input_data:
            # Проверяем тип input_data
            if isinstance(input_data, list):
                for media_data in input_data:
                    if isinstance(media_data, dict):
                        mime_type = media_data.get("mime_type", "")
                        if mime_type.startswith("video/"):
                            has_video = True
                            break
            elif isinstance(input_data, dict):
                # Для одного медиа файла
                mime_type = input_data.get("mime_type", "")
                if mime_type.startswith("video/"):
                    has_video = True

        # Создаем конфигурацию генерации
        # Отключаем thinking для форматирования Perplexity, так как это может мешать правильному форматированию
        # Используем максимальный thinking budget для Gemini 2.5 Flash (1000 токенов)
        if has_video or call_type in ["format_perplexity_output"]:
            thinking_budget = None
        elif call_type == "audio_summary_gemini":
            thinking_budget = 0  # Отключаем thinking для audio_summary_gemini
        else:
            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_FLASH_MAX

        # Специальные параметры для разных типов вызовов
        if call_type == "audio_summary_gemini":
            max_tokens = 8192  # Достаточно для сводок
            temp = 0.4  # Более детерминированный вывод для структурированного формата
        elif call_type == "format_perplexity_output":
            max_tokens = 8192  # Достаточно для форматирования
            temp = 0.4  # Более детерминированный вывод для форматирования
        else:
            max_tokens = 65000
            temp = 0.7

        safety_settings = create_safety_settings()
        generation_config = create_generation_config(
            max_output_tokens=max_tokens,
            temperature=temp,
            top_p=0.9,
            top_k=40,
            thinking_budget=thinking_budget,
            system_instruction=system_prompt,
            safety_settings=safety_settings
        )

        # Выполняем запрос
        try:
            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=generation_config
            )

            if response.text:
                return response.text
            else:
                # ДЕБАГ: Детальное логирование пустого ответа для audio_summary_gemini
                if call_type == "audio_summary_gemini":
                    log_admin(
                        f"{log_prefix}DEBUG_AUDIO_EMPTY_RESPONSE - Empty response from model: "
                        f"Model: {model_name}, "
                        f"Response object: {response}, "
                        f"Contents length: {len(contents)}",
                        level="error"
                    )
                return "Извините, получен пустой ответ от модели."
        except Exception as api_error:
            # ДЕБАГ: Детальное логирование ошибок API для audio_summary_gemini
            if call_type == "audio_summary_gemini":
                log_admin(
                    f"{log_prefix}DEBUG_AUDIO_API_ERROR - API call failed: "
                    f"Error type: {type(api_error).__name__}, "
                    f"Error message: {str(api_error)}, "
                    f"Model: {model_name}, "
                    f"Contents: {contents[:500] if contents else 'None'}",
                    level="error"
                )
            # Перебрасываем исключение для обработки в try_with_fallback
            raise

    # Выполняем с fallback по ключам
    result = try_with_fallback(_generate_content)

    if result is None:
        return "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."

    log_admin(f"{log_prefix}Successful response received (length: {len(result)})")
    return result


@retry_on_censorship(max_attempts=5)
def call_official_gemini_api_genai(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    """
    Миграция call_official_gemini_api на официальную библиотеку genai.
    Общая функция для вызова официального Gemini API.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiOfficial-GenAI] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."




    def _generate_content(client: genai.Client):
        # Определяем, использовать ли историю
        use_history = (
            call_type.startswith("general")      # «general», «general_primary», «general_fallback…»
            or call_type in ["media_group", "file_query"]
        )

        # Дополнительное логирование для специальных типов вызовов
        if call_type == "audio_summary_gemini":
            log_admin(f"{log_prefix}Processing audio summary with system_prompt length: {len(system_prompt) if system_prompt else 0}")
        elif call_type == "format_perplexity_output":
            log_admin(f"{log_prefix}Processing Perplexity formatting with system_prompt length: {len(system_prompt) if system_prompt else 0}")

        # Конвертируем историю в формат GenAI
        contents = convert_history_to_genai_format(history, use_history=use_history)

        # Добавляем текущее сообщение пользователя
        current_parts = []
        if user_text:
            # Очищаем текст от управляющих символов
            import re
            sanitized_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
            if sanitized_text.strip():
                current_parts.append(types.Part(text=sanitized_text))

        # Добавляем медиа данные
        if input_data:
            # Проверяем тип input_data
            if isinstance(input_data, list):
                # Для списка медиа (ограничиваем до 16 изображений)
                media_parts = convert_input_data_to_genai_parts(input_data[:16])
                current_parts.extend(media_parts)
            elif isinstance(input_data, dict):
                # Для одного медиа файла (например, аудио для транскрипции)
                media_parts = convert_input_data_to_genai_parts([input_data])
                current_parts.extend(media_parts)

        if current_parts:
            contents.append(types.UserContent(parts=current_parts))

        if not contents:
            return "Извините, нет данных для обработки запроса."

        # Настройки генерации в зависимости от типа вызова
        if call_type == "html_generation":
            max_tokens = 65000
            temperature = 1.0
        elif call_type in ["podcast_generation", "research_podcast_generation", "research_podcast_diana_sasha_generation"]:
            max_tokens = 65000
            temperature = 1.0  # Температура 2 для генерации подкастов
        elif use_history:
            max_tokens = 65000
            temperature = 1.0
        else:
            max_tokens = 65000
            temperature = 1.0

        # Определяем максимальный thinking budget в зависимости от модели
        thinking_budget = None
        if model_name == "gemini-2.5-pro":
            # Проверяем настройки ultrathink и ultrapro для пользователя
            if user_id:
                try:
                    from bot_globals import get_user_setting
                    ultrathink_enabled = get_user_setting(user_id, "ultrathink_enabled")
                    ultrapro_enabled = get_user_setting(user_id, "ultrapro_enabled")
                    
                    if ultrathink_enabled:
                        thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_ULTRATHINK  # 32768 токенов для глубокого размышления
                        log_admin(f"[{call_type.capitalize()}/GeminiOfficial-GenAI] User {user_id} has ultrathink enabled - using thinking_budget: {thinking_budget}")
                    elif ultrapro_enabled:
                        thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_ULTRATHINK  # 32768 токенов для максимального размышления
                        log_admin(f"[{call_type.capitalize()}/GeminiOfficial-GenAI] User {user_id} has ultrapro enabled - using thinking_budget: {thinking_budget}")
                    else:
                        thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX  # 128 токенов (стандартный)
                except Exception as e:
                    log_admin(f"[{call_type.capitalize()}/GeminiOfficial-GenAI] Error getting ultrathink/ultrapro settings for user {user_id}: {e}", level="error")
                    thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX  # Fallback к стандартному
            else:
                thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX  # 128 токенов (стандартный)
        elif model_name == "gemini-2.5-flash":
            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_FLASH_MAX  # 1000 токенов
        elif model_name == "gemini-2.5-flash-lite-preview-06-17":
            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_FLASH_LITE_MAX  # 1000 токенов

        # Отключаем thinking для специальных случаев где это может мешать форматированию
        if call_type in ["format_perplexity_output"]:
            thinking_budget = None
        elif call_type == "audio_summary_gemini":
            thinking_budget = 0  # Отключаем thinking для audio_summary_gemini

        # Создаем конфигурацию генерации
        safety_settings = create_safety_settings()

        # Добавляем Google Search tool для Gemini 2.5 Pro
        tools = None
        if model_name == "gemini-2.5-pro":
            tools = [
                types.Tool(googleSearch=types.GoogleSearch())
            ]

        generation_config = create_generation_config(
            max_output_tokens=max_tokens,
            temperature=temperature,
            top_p=0.9,
            top_k=40,
            thinking_budget=thinking_budget,
            system_instruction=system_prompt,
            safety_settings=safety_settings,
            tools=tools
        )

        # Выполняем запрос
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=generation_config
        )

        return response.text if response.text else "Извините, получен пустой ответ от модели."

    # Выполняем с fallback по ключам
    result = try_with_fallback(_generate_content)

    if result is None:
        return "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."

    log_admin(f"{log_prefix}Successful response received (length: {len(result)})")
    return result


@retry_on_censorship_async(max_attempts=5)
async def call_gemma_3_1b_api_genai(history, user_text, input_data=None, call_type="general"):
    """
    Миграция call_gemma_3_1b_api на официальную библиотеку genai.
    Асинхронная функция для вызова Gemma-3-27b-it через официальный Gemini API.
    ТОЛЬКО для групповых чатов. С провокационным системным промптом. Температура = 1.2.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemma3-27b-GenAI] "
    model_name = "gemma-3-27b-it"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    async def _generate_content_async(client: genai.Client):
        # Определяем, использовать ли историю
        use_history = (
            call_type.startswith("general")      # «general», «general_primary», «general_fallback…»
            or call_type in ["media_group", "file_query"]
        )

        # Конвертируем историю в формат GenAI
        contents = convert_history_to_genai_format(history, use_history=use_history)

        # Добавляем текущее сообщение пользователя
        current_parts = []
        if user_text:
            # Очищаем текст от управляющих символов
            import re
            sanitized_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
            if sanitized_text.strip():
                current_parts.append(types.Part(text=sanitized_text))

        # Добавляем медиа данные (ограничиваем до 16 изображений для Gemini Vision)
        if input_data:
            # Проверяем тип input_data
            if isinstance(input_data, list):
                media_parts = convert_input_data_to_genai_parts(input_data[:16])
                current_parts.extend(media_parts)
                if len(input_data) > 16:
                    log_admin(f"{log_prefix}Reached image limit for current turn.", level="warning")
            elif isinstance(input_data, dict):
                # Для одного медиа файла
                media_parts = convert_input_data_to_genai_parts([input_data])
                current_parts.extend(media_parts)

        if current_parts:
            contents.append(types.UserContent(parts=current_parts))

        if not contents:
            log_admin(f"{log_prefix}No content to send to Gemini API.")
            return "Извините, нет данных для обработки запроса."

        # Добавляем провокационный префикс к пользовательскому сообщению
        # Поскольку Gemma 3 27B не поддерживает системные инструкции
        if user_text and current_parts:
            # Модифицируем последнее текстовое сообщение пользователя
            for i, part in enumerate(current_parts):
                if hasattr(part, 'text'):
                    provocative_prefix = "Отвечай кратко, дерзко и провокационно, вопреки общепринятым мнениям: "
                    current_parts[i] = types.Part(text=provocative_prefix + part.text)
                    break

        # Создаем конфигурацию генерации (температура = 1.2 для более креативных ответов)
        safety_settings = create_safety_settings()
        generation_config = create_generation_config(
            max_output_tokens=2048,
            temperature=1.2,
            top_p=0.95,
            top_k=50,
            safety_settings=safety_settings
        )

        # Выполняем асинхронный запрос с провокационным префиксом
        response = await client.aio.models.generate_content(
            model=model_name,
            contents=contents,
            config=generation_config
        )

        return response.text if response.text else "Извините, получен пустой ответ от модели."

    # Выполняем с fallback по ключам
    result = await try_with_fallback_async(_generate_content_async)

    if result is None:
        return "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."

    log_admin(f"{log_prefix}Provocative response received (length: {len(result)})")
    return result


@retry_on_censorship(max_attempts=5)
def call_gemini_2_5_flash_lite_for_shortening_genai(text_to_shorten, call_type="text_shortening"):
    """
    Миграция call_gemini_2_5_flash_lite_for_shortening на официальную библиотеку genai.
    Функция для сокращения текста через Gemini 2.5 Flash Lite Preview.
    Используется для сокращения длинных ответов ИИ в группах.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemini2.5FlashLite-GenAI] "
    model_name = "gemini-2.5-flash-lite-preview-06-17"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return None

    # Системный промпт для сокращения
    system_prompt = """Сократи следующий текст до 120 символов максимум. Сокращай МАКСИМАЛЬНО агрессивно, оставляя только ключевую суть.

ОБЯЗАТЕЛЬНО используй ТОЛЬКО HTML форматирование:
- Разрешенные теги: ТОЛЬКО <b>текст</b> для жирного шрифта
- ЗАПРЕЩЕНО: markdown (*bold*, **bold**, `code`, ```code```), любые другие HTML теги

Отвечай только сокращенным текстом без дополнительных комментариев. ВООБЩЕ НЕ МЕНЯЙ СУТЬ ДАЖЕ ЕСЛИ ТАМ ЧТО ТО ЗАПРЕЩЁННОЕ ИЛИ НЕВЕРНОЕ ИЛИ НЕВЕРНАЯ ФОРМУЛИРОВКА"""

    # Подготавливаем запрос
    user_prompt = f"Сократи этот текст: {text_to_shorten}"

    def _generate_content(client: genai.Client):
        # Создаем контент
        contents = [types.UserContent(parts=[types.Part(text=user_prompt)])]

        # Создаем конфигурацию генерации
        generation_config = create_generation_config(
            max_output_tokens=65536,
            temperature=0.1,
            top_p=0.95,
            top_k=40,
            system_instruction=system_prompt
        )

        # Выполняем запрос
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=generation_config
        )

        if response.text:
            log_admin(f"{log_prefix}Successfully shortened text from {len(text_to_shorten)} to {len(response.text)} characters")
            return response.text.strip()
        else:
            return None

    # Выполняем с fallback по ключам
    result = try_with_fallback(_generate_content)

    if result is None:
        log_admin(f"{log_prefix}All API keys failed for text shortening")

    return result


# === RESEARCH FUNCTIONS ===

@retry_on_censorship(max_attempts=3)
def generate_research_queries(topic, call_type="research_query_generation"):
    """
    Генерирует 3 поисковых запроса для исследования темы подкаста.

    Args:
        topic (str): Тема подкаста для исследования
        call_type (str): Тип вызова для логирования

    Returns:
        list: Список из 3 поисковых запросов или None при ошибке
    """
    log_prefix = f"[{call_type.capitalize()}/ResearchQueries-GenAI] "
    model_name = "gemini-2.5-flash"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return None

    if not topic or not isinstance(topic, str):
        log_admin(f"{log_prefix}Invalid topic provided")
        return None

    # Используем системный промпт из конфигурации
    system_prompt = config.SYSTEM_PROMPT_RESEARCH_QUERY_GENERATION.format(user_request=topic)

    # Подготавливаем запрос (промпт уже содержит тему)
    user_prompt = "Создай поисковые запросы согласно инструкции."

    def _generate_content(client: genai.Client):
        # Создаем контент
        contents = [types.UserContent(parts=[types.Part(text=user_prompt)])]

        # Создаем конфигурацию генерации
        generation_config = create_generation_config(
            max_output_tokens=1000,
            temperature=0.3,
            top_p=0.9,
            top_k=40,
            system_instruction=system_prompt
        )

        # Выполняем запрос
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=generation_config
        )

        if response.text:
            # Парсим ответ для извлечения запросов
            import re
            queries = re.findall(r'\[([^\]]+)\]', response.text)

            if len(queries) == 3:
                log_admin(f"{log_prefix}Successfully generated 3 research queries for topic: {topic}")
                return queries
            else:
                log_admin(f"{log_prefix}Generated {len(queries)} queries instead of 3. Raw response: {response.text}")
                # Если получили не 3 запроса, возвращаем то что есть или создаем базовые
                if queries:
                    return queries[:3] if len(queries) > 3 else queries
                else:
                    # Fallback: создаем базовые запросы
                    return [f"{topic} обзор", f"{topic} последние новости", f"{topic} экспертное мнение"]
        else:
            log_admin(f"{log_prefix}Empty response received")
            return None

    # Выполняем с fallback по ключам
    result = try_with_fallback(_generate_content)

    if result is None:
        log_admin(f"{log_prefix}All API keys failed for research query generation")
        # Fallback: создаем базовые запросы
        return [f"{topic} обзор", f"{topic} последние новости", f"{topic} экспертное мнение"]

    return result


def perform_web_search(query, call_type="web_search"):
    """
    Выполняет веб-поиск по одному запросу используя GoogleSearch.

    Args:
        query (str): Поисковый запрос
        call_type (str): Тип вызова для логирования

    Returns:
        str: Результат поиска или None при ошибке
    """
    log_prefix = f"[{call_type.capitalize()}/WebSearch-GenAI] "
    model_name = "gemini-2.5-flash"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return None

    if not query or not isinstance(query, str):
        log_admin(f"{log_prefix}Invalid query provided")
        return None

    def _search_content(client: genai.Client):
        try:
            # Создаем инструмент поиска
            search_tool = types.Tool(
                google_search=types.GoogleSearch()
            )

            # Конфигурация запроса с поиском
            config_with_search = types.GenerateContentConfig(
                tools=[search_tool],
                max_output_tokens=4000,
                temperature=0.1,
                top_p=0.9,
                top_k=40
            )

            # Выполняем запрос с поиском
            response = client.models.generate_content(
                model=model_name,
                contents=f"Найди актуальную информацию по запросу: {query}",
                config=config_with_search
            )

            if response.text:
                log_admin(f"{log_prefix}Successfully performed search for query: {query[:50]}...")
                return response.text
            else:
                log_admin(f"{log_prefix}Empty search response for query: {query}")
                return None

        except Exception as e:
            log_admin(f"{log_prefix}Error during search for query '{query}': {e}")
            return None

    # Выполняем с fallback по ключам
    result = try_with_fallback(_search_content)

    if result is None:
        log_admin(f"{log_prefix}All API keys failed for web search")

    return result


async def perform_multiple_searches(queries, progress_callback=None, call_type="multiple_web_search"):
    """
    Асинхронно выполняет несколько веб-поисков параллельно.

    Args:
        queries (list): Список поисковых запросов
        progress_callback (callable): Функция для обновления прогресса (опционально)
        call_type (str): Тип вызова для логирования

    Returns:
        list: Список результатов поиска в том же порядке, что и запросы
    """
    log_prefix = f"[{call_type.capitalize()}/MultipleWebSearch-GenAI] "

    if not queries or not isinstance(queries, list):
        log_admin(f"{log_prefix}Invalid queries list provided")
        return []

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return []

    log_admin(f"{log_prefix}Starting {len(queries)} parallel searches")

    async def _search_single_async(query, index):
        """Асинхронно выполняет один поисковый запрос."""
        model_name = "gemini-2.5-flash"

        async def _search_content_async(client: genai.Client):
            try:
                # Создаем инструмент поиска
                search_tool = types.Tool(
                    google_search=types.GoogleSearch()
                )

                # Конфигурация запроса с поиском
                config_with_search = types.GenerateContentConfig(
                    tools=[search_tool],
                    max_output_tokens=4000,
                    temperature=0.1,
                    top_p=0.9,
                    top_k=40
                )

                # Выполняем асинхронный запрос с поиском
                response = await client.aio.models.generate_content(
                    model=model_name,
                    contents=f"Найди актуальную информацию по запросу: {query}",
                    config=config_with_search
                )

                if response.text:
                    log_admin(f"{log_prefix}Search {index+1} completed for query: {query[:50]}...")

                    # Вызываем callback для обновления прогресса
                    if progress_callback:
                        try:
                            await progress_callback(index + 1, len(queries))
                        except Exception as e:
                            log_admin(f"{log_prefix}Error in progress callback: {e}")

                    return response.text
                else:
                    log_admin(f"{log_prefix}Empty search response for query {index+1}: {query}")
                    return None

            except Exception as e:
                log_admin(f"{log_prefix}Error during search {index+1} for query '{query}': {e}")
                return None

        # Выполняем с fallback по ключам
        result = await try_with_fallback_async(_search_content_async)

        if result is None:
            log_admin(f"{log_prefix}All API keys failed for search {index+1}")
            return f"Поиск по запросу '{query}' не удался"

        return result

    # Создаем задачи для параллельного выполнения
    search_tasks = []
    for i, query in enumerate(queries):
        task = _search_single_async(query, i)
        search_tasks.append(task)

    try:
        # Выполняем все поиски параллельно с таймаутом
        results = await asyncio.wait_for(
            asyncio.gather(*search_tasks, return_exceptions=True),
            timeout=30.0  # Максимум 30 секунд на все поиски
        )

        # Обрабатываем результаты
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                log_admin(f"{log_prefix}Search {i+1} failed with exception: {result}")
                processed_results.append(f"Поиск по запросу '{queries[i]}' завершился с ошибкой")
            elif result is None:
                processed_results.append(f"Поиск по запросу '{queries[i]}' не дал результатов")
            else:
                processed_results.append(result)

        log_admin(f"{log_prefix}Completed {len(queries)} searches, {len([r for r in processed_results if not r.startswith('Поиск по запросу')])} successful")
        return processed_results

    except asyncio.TimeoutError:
        log_admin(f"{log_prefix}Multiple searches timed out after 30 seconds")
        return [f"Поиск по запросу '{query}' превысил время ожидания" for query in queries]
    except Exception as e:
        log_admin(f"{log_prefix}Error during multiple searches: {e}")
        return [f"Поиск по запросу '{query}' завершился с ошибкой" for query in queries]


# === TESTING FUNCTIONS ===

def test_research_functions():
    """
    Тестовая функция для проверки работы функций ресерча.
    Используется для тестирования этапа 1.
    """
    print("=== ТЕСТИРОВАНИЕ ФУНКЦИЙ РЕСЕРЧА ===")

    # Тест 1: Генерация поисковых запросов
    print("\n1. Тестирование генерации поисковых запросов...")
    test_topic = "искусственный интеллект в медицине"
    queries = generate_research_queries(test_topic)

    if queries:
        print(f"✅ Успешно сгенерированы запросы для темы '{test_topic}':")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. {query}")
    else:
        print("❌ Ошибка генерации запросов")
        return False

    # Тест 2: Одиночный поиск
    print("\n2. Тестирование одиночного поиска...")
    if queries:
        test_query = queries[0]
        search_result = perform_web_search(test_query)

        if search_result:
            print(f"✅ Успешно выполнен поиск по запросу '{test_query}'")
            print(f"   Длина результата: {len(search_result)} символов")
        else:
            print(f"❌ Ошибка поиска по запросу '{test_query}'")
            return False

    print("\n✅ Все тесты пройдены успешно!")
    return True


async def test_multiple_searches():
    """
    Асинхронная тестовая функция для проверки множественного поиска.
    """
    print("\n=== ТЕСТИРОВАНИЕ АСИНХРОННОГО МНОЖЕСТВЕННОГО ПОИСКА ===")

    # Генерируем запросы
    test_topic = "Python программирование"
    queries = generate_research_queries(test_topic)

    if not queries:
        print("❌ Не удалось сгенерировать запросы для тестирования")
        return False

    print(f"Тестируем с запросами: {queries}")

    # Функция для отслеживания прогресса
    async def progress_callback(completed, total):
        print(f"   Прогресс: {completed}/{total} поисков завершено")

    # Выполняем множественный поиск
    results = await perform_multiple_searches(queries, progress_callback)

    if results and len(results) == len(queries):
        print(f"✅ Успешно выполнены все {len(queries)} поиска")
        for i, result in enumerate(results, 1):
            if result and not result.startswith("Поиск по запросу"):
                print(f"   Результат {i}: {len(result)} символов")
            else:
                print(f"   Результат {i}: {result}")
        return True
    else:
        print("❌ Ошибка множественного поиска")
        return False


# --- Streaming Functions ---

@retry_on_censorship(max_attempts=5)
def call_official_gemini_api_genai_stream(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    """
    Стриминговая версия call_official_gemini_api_genai с правильной обработкой chunks.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiOfficial-GenAI-Stream] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        yield "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."
        return

    def _generate_stream(client: genai.Client):
        try:
            # Подготовка содержимого
            contents = []
            use_history = (
                call_type.startswith("general")
                or call_type in ["media_group", "file_query"]
            )

            # Конвертируем историю
            if use_history and history:
                history_contents = convert_history_to_genai_format(history, use_history=True)
                contents.extend(history_contents)

            # Добавляем текущее сообщение пользователя
            current_parts = []
            if user_text:
                import re
                sanitized_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
                if sanitized_text.strip():
                    current_parts.append(types.Part(text=sanitized_text))

            # Добавляем медиа данные
            if input_data:
                if isinstance(input_data, list):
                    media_parts = convert_input_data_to_genai_parts(input_data)
                    current_parts.extend(media_parts)
                elif isinstance(input_data, dict):
                    media_parts = convert_input_data_to_genai_parts([input_data])
                    current_parts.extend(media_parts)

            if current_parts:
                contents.append(types.UserContent(parts=current_parts))

            if not contents:
                yield "Извините, нет данных для обработки запроса."
                return

            # Определяем thinking budget
            thinking_budget = None
            if model_name == "gemini-2.5-pro":
                if user_id:
                    try:
                        from bot_globals import get_user_setting
                        ultrathink_enabled = get_user_setting(user_id, "ultrathink_enabled")
                        ultrapro_enabled = get_user_setting(user_id, "ultrapro_enabled")

                        if ultrapro_enabled:
                            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_ULTRATHINK
                        elif ultrathink_enabled:
                            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_ULTRATHINK
                        else:
                            thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX
                    except Exception:
                        thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX
                else:
                    thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_PRO_MAX
            elif model_name == "gemini-2.5-flash":
                thinking_budget = config.THINKING_BUDGET_GEMINI_2_5_FLASH_MAX

            # Создаем конфигурацию
            safety_settings = create_safety_settings()
            tools = None
            if model_name == "gemini-2.5-pro":
                tools = [types.Tool(googleSearch=types.GoogleSearch())]

            generation_config = create_generation_config(
                max_output_tokens=65536,
                temperature=0.7,
                top_p=0.95,
                top_k=40,
                thinking_budget=thinking_budget,
                system_instruction=system_prompt,
                safety_settings=safety_settings,
                tools=tools
            )

            log_admin(f"{log_prefix}Debug: contents length = {len(contents)}")
            log_admin(f"{log_prefix}Debug: system_prompt length = {len(system_prompt) if system_prompt else 0}")
            log_admin(f"{log_prefix}Debug: model_name = {model_name}")
            log_admin(f"{log_prefix}Starting streaming generation with model {model_name}")

            # ИСПРАВЛЕННАЯ ЛОГИКА СТРИМИНГА
            accumulated_text = ""
            chunk_count = 0

            # Используем правильный метод для стриминга
            stream = client.models.generate_content_stream(
                model=model_name,
                contents=contents,
                config=generation_config
            )

            # Обрабатываем stream правильно
            for chunk in stream:
                chunk_count += 1
                chunk_text = ""

                # Извлекаем текст из chunk
                if hasattr(chunk, 'text') and chunk.text:
                    chunk_text = chunk.text
                elif hasattr(chunk, 'candidates') and chunk.candidates:
                    for candidate in chunk.candidates:
                        if hasattr(candidate, 'content') and candidate.content:
                            for part in candidate.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    chunk_text += part.text

                if chunk_text:
                    accumulated_text += chunk_text

                    # НОВАЯ ЛОГИКА: отправляем сообщение после 3 символов, затем каждые 5
                    if len(accumulated_text) >= 3 and (len(accumulated_text) % 5 == 0 or len(accumulated_text) == 3):
                        yield chunk_text

            log_admin(f"{log_prefix}Streaming completed. Total chunks: {chunk_count}, Total length: {len(accumulated_text)}")

            if not accumulated_text:
                log_admin(f"{log_prefix}WARNING: No text received from streaming API")
                yield ""

        except Exception as e:
            error_msg = f"Ошибка при стриминговом запросе к {model_name}: {str(e)}"
            log_admin(f"{log_prefix}Error: {error_msg}")
            yield f"Извините, произошла ошибка: {str(e)}"

    # Пробуем с разными ключами
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    for attempt, api_key in enumerate(available_keys, 1):
        log_admin(f"{log_prefix}Attempting streaming with key ending ...{api_key[-4:]} (attempt {attempt}/{len(available_keys)})")

        try:
            client = client_manager.get_client(api_key)
            stream_started = False
            chunk_yielded = False

            for chunk in _generate_stream(client):
                stream_started = True
                chunk_yielded = True
                yield chunk

            if stream_started and chunk_yielded:
                return
            else:
                log_admin(f"{log_prefix}Stream was empty, trying next key...")
                continue

        except Exception as e:
            log_admin(f"{log_prefix}Attempt {attempt} failed: {e}")
            if attempt == len(available_keys):
                yield "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."


@retry_on_censorship(max_attempts=5)
def call_gemini_2_5_flash_api_genai_stream(history, user_text, input_data=None, system_prompt=None, call_type="general"):
    """
    Стриминговая версия call_gemini_2_5_flash_api_genai для личных чатов.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemini2.5Flash-GenAI-Stream] "
    model_name = "gemini-2.5-flash"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        yield "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."
        return

    def _generate_stream(client: genai.Client):
        try:
            contents = []
            history_contents = convert_history_to_genai_format(history, use_history=True)
            contents.extend(history_contents)

            current_parts = []
            if user_text:
                current_parts.append(types.Part(text=user_text))

            if input_data:
                if isinstance(input_data, list):
                    media_parts = convert_input_data_to_genai_parts(input_data)
                    current_parts.extend(media_parts)
                elif isinstance(input_data, dict):
                    media_parts = convert_input_data_to_genai_parts([input_data])
                    current_parts.extend(media_parts)

            if current_parts:
                contents.append(types.UserContent(parts=current_parts))

            safety_settings = create_safety_settings()
            tools = [
                types.Tool(codeExecution=types.ToolCodeExecution()),
                types.Tool(googleSearch=types.GoogleSearch())
            ]

            generation_config = create_generation_config(
                max_output_tokens=65536,
                temperature=0.7,
                top_p=0.9,
                top_k=40,
                thinking_budget=config.THINKING_BUDGET_GEMINI_2_5_FLASH_MAX,
                system_instruction=system_prompt,
                safety_settings=safety_settings,
                tools=tools
            )

            log_admin(f"{log_prefix}Debug: contents length = {len(contents)}")
            log_admin(f"{log_prefix}Debug: system_prompt length = {len(system_prompt) if system_prompt else 0}")
            log_admin(f"{log_prefix}Debug: model_name = {model_name}")
            log_admin(f"{log_prefix}Starting streaming generation with tools enabled")

            accumulated_text = ""
            chunk_count = 0

            stream = client.models.generate_content_stream(
                model=model_name,
                contents=contents,
                config=generation_config
            )

            for chunk in stream:
                chunk_count += 1
                chunk_text = ""

                if hasattr(chunk, 'text') and chunk.text:
                    chunk_text = chunk.text
                elif hasattr(chunk, 'candidates') and chunk.candidates:
                    for candidate in chunk.candidates:
                        if hasattr(candidate, 'content') and candidate.content:
                            for part in candidate.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    chunk_text += part.text

                if chunk_text:
                    accumulated_text += chunk_text

                    # Отправляем после 3 символов, затем каждые 5
                    if len(accumulated_text) >= 3 and (len(accumulated_text) % 5 == 0 or len(accumulated_text) == 3):
                        yield chunk_text

            log_admin(f"{log_prefix}Streaming completed. Total chunks: {chunk_count}, Total length: {len(accumulated_text)}")

            if not accumulated_text:
                log_admin(f"{log_prefix}WARNING: No text received from streaming API")
                yield ""

        except Exception as e:
            error_msg = f"Ошибка при стриминговом запросе к {model_name}: {str(e)}"
            log_admin(f"{log_prefix}Error: {error_msg}")
            yield f"Извините, произошла ошибка: {str(e)}"

    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    for attempt, api_key in enumerate(available_keys, 1):
        log_admin(f"{log_prefix}Attempting streaming with key ending ...{api_key[-4:]} (attempt {attempt}/{len(available_keys)})")

        try:
            client = client_manager.get_client(api_key)
            stream_started = False
            chunk_yielded = False

            for chunk in _generate_stream(client):
                stream_started = True
                chunk_yielded = True
                yield chunk

            if stream_started and chunk_yielded:
                return
            else:
                log_admin(f"{log_prefix}Stream was empty, trying next key...")
                continue

        except Exception as e:
            log_admin(f"{log_prefix}Attempt {attempt} failed: {e}")
            if attempt == len(available_keys):
                yield "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."


def debug_stream_response(stream_response):
    """
    Отладочная функция для анализа ответа стрима.
    """
    try:
        log_admin(f"Stream response type: {type(stream_response)}")
        log_admin(f"Stream response dir: {dir(stream_response)}")

        if hasattr(stream_response, '__iter__'):
            log_admin("Stream response is iterable")
        else:
            log_admin("Stream response is NOT iterable")

    except Exception as e:
        log_admin(f"Error debugging stream response: {e}")
