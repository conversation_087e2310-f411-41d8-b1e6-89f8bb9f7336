# Оптимизированная версия подкастового бота
# Исправлены проблемы с производительностью:
# - Увеличены интервалы фоновых задач
# - Оптимизирован polling
# - Улучшено управление ресурсами

import subprocess
import sys
import time
import traceback
import requests
import telebot
import threading
import os # Добавлено для проверки существования файла лога
from console_cleaner import clear_console, minimize_console_buffer

# Import functions and variables from other modules
# Важно, чтобы bot_globals импортировался одним из первых, чтобы логгер настроился
from bot_globals import (
    bot, log_admin, audio_video_groups, media_groups,
    user_forward_batch, user_request_buffer, log_file_path,
    load_user_settings, save_user_settings,
    audio_video_group_lock, media_group_lock,
    user_forward_batch_lock, user_request_buffer_lock,
    get_bot_username  # ЭТАП 2: Добавлен импорт кэшированной функции
)
from telegraph_helpers import setup_telegraph
from config import TELEGRAPH_SHORT_NAME, AUTO_CLEANUP_ENABLED

# IMPORTANT: Import admin_system to load bot data at startup
import admin_system

# IMPORTANT: Import handlers to register them
import handlers

def start_bot():
    """Функция для запуска бота из других модулей"""
    # Минимизируем буфер консоли для экономии памяти
    minimize_console_buffer()
    log_admin("🚀 Запуск бота через start_bot()...", level="warning")
    
    # Весь код запуска перенесен в эту функцию
    _run_bot_initialization()

def _run_bot_initialization():
    """Внутренняя функция с основной логикой запуска бота"""

    # Проверка существования файла лога после инициализации логгера в bot_globals
    # Эта проверка больше для информации, сам файл создается FileHandler'ом
    if os.path.exists(log_file_path):
        log_admin(f"Log file is expected at: {log_file_path}", level="info")
    else:
        # Попытка создать пустой файл, чтобы проверить права на запись в директорию
        try:
            with open(log_file_path, 'a') as f:
                pass # Просто создать/открыть и закрыть
            log_admin(f"Log file was not found, but successfully created a placeholder at: {log_file_path}. Check permissions if still no logs.", level="info")
            # Важно: FileHandler в logging создаст файл сам, если его нет, при первой записи.
            # Эта проверка здесь - дополнительная, на случай если FileHandler не может его создать.
        except Exception as e:
            log_admin(f"Could not create a placeholder log file at {log_file_path}. Error: {e}. Check directory permissions.", level="error")


    # Check for ffmpeg
    log_admin("Checking for ffmpeg...", level="info")
    try:
        # Сначала проверяем глобальную папку bin (относительно корня проекта)
        current_dir = os.getcwd()
        parent_dir = os.path.dirname(current_dir)  # Поднимаемся на уровень выше (корень проекта)
        global_bin_path = os.path.join(parent_dir, "bin")

        env = os.environ.copy()

        # Добавляем глобальную папку bin в PATH если она существует
        if os.path.exists(global_bin_path):
            env['PATH'] = global_bin_path + os.pathsep + env.get('PATH', '')
            log_admin(f"Added global bin directory to PATH: {global_bin_path}", level="info")

        # Также добавляем системные пути
        env['PATH'] = '/usr/bin:' + env.get('PATH', '')

        process = subprocess.run(
            ['ffmpeg', '-version'],
            check=True,
            capture_output=True,
            timeout=15, # Увеличен тайм-аут
            env=env
        )
        log_admin("ffmpeg found.", level="info")
    except (FileNotFoundError, subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        log_admin("ffmpeg не найден - обработка аудио/видео будет недоступна", level="info")
        # Не выходим, а просто выводим предупреждение
    except Exception as e:
        log_admin(f"Unexpected error during ffmpeg check: {e}", level="error")
        log_admin("Warning: Could not verify ffmpeg, audio/video processing might not work.", level="warning")



    # Initialize Telegraph client
    log_admin("Initializing telegra.ph client...", level="info")
    try:
        # ЭТАП 2: Используем кэшированную функцию вместо bot.get_me()
        bot_username = get_bot_username()
        if bot_username == 'unknown_bot':
            bot_username = None
    except Exception as e:
        log_admin(f"Ошибка при получении username бота для Telegraph: {e}", level="warning")
        bot_username = None
    if not setup_telegraph(short_name=bot_username or TELEGRAPH_SHORT_NAME):
        log_admin("telegra.ph не подключен - публикация отключена", level="info")
    else:
        log_admin("telegra.ph client ready.", level="info")

    # Загрузка настроек пользователей
    log_admin("Загрузка настроек пользователей...", level="info")
    if load_user_settings():
        log_admin("Настройки пользователей успешно загружены.", level="info")
    else:
        log_admin("Используются настройки пользователей по умолчанию.", level="info")

    # Инициализация базы данных
    log_admin("Инициализация базы данных...", level="info")
    try:
        from database import init_database
        if init_database():
            log_admin("База данных успешно инициализирована.", level="info")
        else:
            log_admin("Ошибка инициализации базы данных.", level="error")
    except Exception as e:
        log_admin("Не удалось инициализировать базу данных", level="critical")

    # Объединенный фоновый планировщик для всех периодических задач
    def unified_background_scheduler():
        """
        Объединенный планировщик для всех фоновых задач.
        Уменьшает количество потоков и оптимизирует использование ресурсов.
        ЭТАП 2: Увеличена частота GC до каждых 15 минут, добавлена очистка буферов каждые 20 минут.
        """
        console_clear_counter = 0
        podcast_check_counter = 0
        memory_check_counter = 0
        buffer_cleanup_counter = 0  # ЭТАП 2: Счетчик для очистки буферов

        # Импорты внутри функции для ленивой загрузки
        from rate_limiter import rate_limiter
        from admin_system import run_scheduled_podcasts
        from thread_pool_manager import cleanup_thread_pool
        from bot_globals import cleanup_all_expired_data  # ЭТАП 2: Импорт функции очистки

        log_admin("Unified background scheduler started", level="info")

        while True:
            try:
                # Основной цикл каждые 5 минут для более частой очистки
                time.sleep(300)  # 5 минут

                # Периодическая очистка данных
                try:
                    import gc
                    gc.collect()
                    log_admin("Scheduled garbage collection completed (15min)", level="debug")
                except Exception as e:
                    log_admin(f"Error in scheduled garbage collection: {e}", level="warning")

                # ЭТАП 2: Очистка буферов каждые 20 минут (каждые 1.33 цикла)
                if buffer_cleanup_counter % 1 == 0 and (buffer_cleanup_counter * 15) % 20 == 0:
                    try:
                        # Принудительная очистка user_request_buffer и user_forward_batch
                        cleaned_count = cleanup_all_expired_data()
                        log_admin(f"Buffer cleanup completed: {cleaned_count} items cleaned", level="debug")
                    except Exception as e:
                        log_admin(f"Error in buffer cleanup: {e}", level="warning")

                # ЭТАП 2: Пересчитаны интервалы для 15-минутных циклов
                # Rate limiter cleanup каждые 8 циклов (2 часа)
                if console_clear_counter % 8 == 0:
                    try:
                        rate_limiter.cleanup_old_data()
                        log_admin("Rate limiter cleanup completed", level="debug")
                    except Exception as e:
                        log_admin(f"Error in rate limiter cleanup: {e}", level="warning")

                # Периодический разогрев соединений каждые 12 циклов (3 часа)
                if console_clear_counter % 12 == 0 and console_clear_counter > 0:
                    try:
                        from genai_client import client_manager
                        # Быстрый разогрев 1 ключа для поддержания соединений
                        warmup_results = client_manager.warmup_connections(max_keys=1)
                        if any(warmup_results.values()):
                            log_admin("Periodic connection warmup successful", level="debug")
                    except Exception as e:
                        log_admin(f"Error in periodic warmup: {e}", level="debug")

                # Сброс состояний ключей каждые 24 цикла (6 часов) для восстановления после rate limit
                if console_clear_counter % 24 == 0 and console_clear_counter > 0:
                    try:
                        from genai_client import client_manager
                        client_manager.reset_key_states()
                        log_admin("API key states reset for recovery", level="debug")
                    except Exception as e:
                        log_admin(f"Error resetting key states: {e}", level="debug")

                # Очистка консоли каждые 48 циклов (12 часов)
                if console_clear_counter % 48 == 0 and console_clear_counter > 0:
                    try:
                        clear_console()
                        log_admin("🧹 Консоль очищена для экономии памяти", level="info")
                    except Exception as e:
                        log_admin(f"Error clearing console: {e}", level="warning")

                # Очистка пула потоков каждые 16 циклов (4 часа)
                if console_clear_counter % 16 == 0:
                    try:
                        cleanup_thread_pool()
                        log_admin("Thread pool cleanup completed", level="debug")
                    except Exception as e:
                        log_admin(f"Error in thread pool cleanup: {e}", level="warning")

                    # Очистка висящих FFmpeg процессов
                    try:
                        from utils import cleanup_hanging_ffmpeg_processes
                        cleanup_hanging_ffmpeg_processes()
                        log_admin("FFmpeg process cleanup completed", level="debug")
                    except Exception as e:
                        log_admin(f"Error in FFmpeg process cleanup: {e}", level="warning")

                # Проверка запланированных подкастов каждые 4 цикла (1 час)
                if podcast_check_counter % 4 == 0:
                    try:
                        # Запускаем проверку подкастов в отдельной задаче
                        from thread_pool_manager import submit_task
                        submit_task(run_scheduled_podcasts)
                        log_admin("Scheduled podcast check submitted", level="debug")
                    except Exception as e:
                        log_admin(f"Error submitting scheduled podcast check: {e}", level="warning")

                console_clear_counter += 1
                podcast_check_counter += 1
                memory_check_counter += 1
                buffer_cleanup_counter += 1  # ЭТАП 2: Добавлен счетчик буферов

                # Сброс счетчиков для предотвращения переполнения
                if console_clear_counter > 1000:
                    console_clear_counter = 1
                if podcast_check_counter > 1000:
                    podcast_check_counter = 1
                if memory_check_counter > 1000:
                    memory_check_counter = 1
                if buffer_cleanup_counter > 1000:  # ЭТАП 2: Добавлен сброс счетчика буферов
                    buffer_cleanup_counter = 1

            except Exception as e:
                log_admin(f"Error in unified background scheduler: {e}", level="error")
                time.sleep(300)  # При ошибке ждем 5 минут

    # Запуск объединенного планировщика
    unified_thread = threading.Thread(target=unified_background_scheduler, daemon=True)
    unified_thread.start()

    # Планировщик очистки памяти уже интегрирован в unified_background_scheduler
    # и cleanup_scheduler, поэтому отдельный планировщик не нужен

    log_admin("Unified background scheduler thread started (оптимизированный).", level="info")

    # Запуск разогрева соединений в фоновом режиме для быстрого отклика
    def warmup_connections_background():
        """Фоновый разогрев соединений для быстрого отклика после простоя."""
        try:
            # Ждем 5 секунд после запуска, чтобы основные системы инициализировались
            time.sleep(5)
            
            from genai_client import client_manager
            log_admin("🔥 Запуск разогрева соединений для быстрого отклика...", level="info")
            
            # Разогреваем первые 2 ключа для быстрого старта
            warmup_results = client_manager.warmup_connections(max_keys=2)
            successful_count = sum(1 for success in warmup_results.values() if success)
            
            if successful_count > 0:
                log_admin(f"✅ Разогрев завершен: {successful_count} соединений готовы", level="info")
            else:
                log_admin("⚠️ Разогрев не удался, но бот продолжит работу", level="warning")
                
        except Exception as e:
            log_admin(f"Ошибка при разогреве соединений: {e}", level="warning")

    # Запускаем разогрев в отдельном потоке
    warmup_thread = threading.Thread(target=warmup_connections_background, daemon=True)
    warmup_thread.start()

    # Podcast queue update scheduler removed - podcasts now run directly without queue

    # Запуск планировщика автоочистки (только если включен в конфигурации)
    if AUTO_CLEANUP_ENABLED:
        log_admin("Starting cleanup scheduler...", level="info")
        try:
            def cleanup_scheduler():
                """Планировщик автоочистки ресурсов."""
                import time
                from cleanup_manager import cleanup_manager, check_resource_limits

                # Первая очистка через 5 минут после запуска
                time.sleep(300)

                while True:
                    try:

                        # Проверяем лимиты ресурсов
                        warnings = check_resource_limits()
                        if warnings:
                            log_admin(f"Превышены лимиты ресурсов ({len(warnings)} предупреждений)", level="warning")

                            # Выполняем полную очистку при превышении лимитов
                            log_admin("Запуск экстренной очистки из-за превышения лимитов...", level="info")
                            cleanup_manager.full_cleanup()

                            # Принудительная сборка мусора после экстренной очистки
                            import gc
                            gc.collect()
                        else:
                            # Обычная периодическая очистка (еще реже)
                            log_admin("Запуск периодической очистки ресурсов...", level="info")
                            cleanup_manager.cleanup_temp_files()
                            cleanup_manager.cleanup_python_cache()

                            # Ротация логов и оптимизация БД раз в день
                            import datetime
                            current_hour = datetime.datetime.now().hour
                            if current_hour == 3:  # В 3 утра
                                cleanup_manager.rotate_logs()
                                cleanup_manager.optimize_database()

                                # Принудительная сборка мусора после ночной очистки
                                import gc
                                gc.collect()

                        # Следующая проверка через 6 часов (увеличено с 4 часов)
                        time.sleep(21600)

                    except Exception as e:
                        log_admin(f"Ошибка в планировщике очистки: {e}", level="error")
                        time.sleep(10800)  # При ошибке ждем 3 часа (увеличено с 2 часов)

            cleanup_thread = threading.Thread(target=cleanup_scheduler, daemon=True)
            cleanup_thread.start()
            log_admin("Cleanup scheduler thread started (оптимизированный).", level="info")
        except Exception as e:
            log_admin(f"Error starting cleanup scheduler: {e}", level="error")
    else:
        log_admin("Cleanup scheduler disabled by configuration (AUTO_CLEANUP_ENABLED=False)", level="info")



    log_admin(f"Bot sh is starting...", level="info")
    # === ИНИЦИАЛИЗАЦИЯ ЗАВЕРШЕНА ===
    # log_admin("Bot started polling.", level="info") # This line is removed

    try:
        log_admin("MAIN: Bot polling starting...", level="info")
        
        # Счетчики для адаптивного переподключения
        consecutive_timeout_errors = 0
        consecutive_connection_errors = 0
        consecutive_api_errors = 0
        
        # Используем infinity_polling для автоматического переподключения
        try:
            # Увеличенный timeout для предотвращения ложных срабатываний при сжатии логов
            bot.infinity_polling(interval=1, timeout=90, skip_pending=True,
                               allowed_updates=['message', 'callback_query', 'inline_query', 'chosen_inline_result',
                                              'message_reaction', 'message_reaction_count'])
        except KeyboardInterrupt:
            log_admin("🛑 Получен сигнал остановки бота", level="info")
        except Exception as e:
            log_admin(f"❌ Критическая ошибка в infinity_polling: {e}", level="error")
            # Fallback на старый метод с увеличенным timeout
            while True:
                try:
                    # Увеличенный timeout с 30 до 90 секунд для предотвращения ложных срабатываний
                    bot.polling(non_stop=True, interval=1, timeout=90, skip_pending=True,
                               allowed_updates=['message', 'callback_query', 'inline_query', 'chosen_inline_result',
                                              'message_reaction', 'message_reaction_count'])
                    
                    # Если polling завершился без ошибок, сбрасываем счетчики
                    consecutive_timeout_errors = 0
                    consecutive_connection_errors = 0
                    consecutive_api_errors = 0
                    
                except requests.exceptions.ReadTimeout:
                    consecutive_timeout_errors += 1
                    log_admin(f"❌ Таймаут подключения к Telegram (попытка #{consecutive_timeout_errors})", level="warning")

                    # Адаптивная задержка для таймаутов
                    delay = min(consecutive_timeout_errors * 2, 30) # Экспоненциальная задержка до 30с

                    log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                    time.sleep(delay)
                
                except requests.exceptions.ConnectionError:
                    consecutive_connection_errors += 1
                    log_admin(f"❌ Ошибка сети при подключении к Telegram (попытка #{consecutive_connection_errors})", level="warning")

                    # Адаптивная задержка для ошибок сети
                    delay = min(consecutive_connection_errors * 2, 60) # Экспоненциальная задержка до 60с

                    log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                    time.sleep(delay)
                
                except telebot.apihelper.ApiTelegramException as e_api:
                    consecutive_api_errors += 1
                    log_admin(f"❌ Ошибка Telegram API (попытка #{consecutive_api_errors}): {e_api}", level="error")

                    # Для API ошибок немного больше задержка
                    if consecutive_api_errors == 1:
                        delay = 1.0
                    elif consecutive_api_errors <= 3:
                        delay = 2.0
                    elif consecutive_api_errors <= 5:
                        delay = 5.0
                    else:
                        delay = 10.0  # Максимальная задержка 10 секунд

                    log_admin(f"🔄 Переподключение через {delay}с...", level="info")
                    time.sleep(delay)

                except Exception as e_inner:
                    log_admin(f"💥 Критическая ошибка: {e_inner}", level="critical")
                    log_admin(f"Exception traceback: {traceback.format_exc()}", level="critical")

                # Cleanup hanging processes before restart
                try:
                    from utils import cleanup_hanging_ffmpeg_processes
                    cleanup_hanging_ffmpeg_processes()
                    log_admin("Emergency FFmpeg cleanup completed", level="info")
                except Exception as cleanup_error:
                    log_admin(f"Error in emergency cleanup: {cleanup_error}", level="warning")

                # Для критических ошибок даем больше времени на восстановление
                log_admin("🔄 Перезапуск polling через 5с (критическая ошибка)...", level="info")
                time.sleep(5)
                
                # Сбрасываем счетчики после критической ошибки
                consecutive_timeout_errors = 0
                consecutive_connection_errors = 0
                consecutive_api_errors = 0
                
            else:
                log_admin("⚠️ Polling неожиданно завершен", level="warning")
    except Exception as e_poll_outer: # Catch exceptions from the setup before while True, or if while True is broken
        log_admin("Фатальная ошибка polling", level="critical")
    finally:
        log_admin("Цикл polling завершен или прерван", level="critical")

    # --- Cleanup on exit ---
    log_admin("Stopping bot...", level="info")

    with audio_video_group_lock:
        for user_id_key in list(audio_video_groups.keys()): # Изменено имя переменной во избежание конфликта
            if audio_video_groups[user_id_key]['timer']:
                audio_video_groups[user_id_key]['timer'].cancel()
                log_admin(f"Cancelled pending audio timer for user {user_id_key} on stop.", level="debug")

    with media_group_lock:
        for group_id in list(media_groups.keys()):
            if media_groups[group_id]['timer']:
                media_groups[group_id]['timer'].cancel()
                log_admin(f"Cancelled pending media group timer {group_id} on stop.", level="debug")

    with user_forward_batch_lock:
        for user_id_key_fw in list(user_forward_batch.keys()): # Изменено имя переменной
            if user_forward_batch[user_id_key_fw]['timer']:
                user_forward_batch[user_id_key_fw]['timer'].cancel()
                log_admin(f"Cancelled pending forward batch timer for user {user_id_key_fw} on stop.", level="debug")

    with user_request_buffer_lock:
        for user_id_key_req in list(user_request_buffer.keys()): # Изменено имя переменной
            if user_request_buffer[user_id_key_req]['timer']:
                user_request_buffer[user_id_key_req]['timer'].cancel()
                log_admin(f"Cancelled pending request buffer timer for user {user_id_key_req} on stop.", level="debug")

    # Сохранение настроек пользователей при завершении работы
    log_admin("Сохранение настроек пользователей перед завершением...", level="info")
    if save_user_settings():
        log_admin("Настройки пользователей успешно сохранены.", level="info")
    else:
        log_admin("Ошибка при сохранении настроек пользователей!", level="error")

    log_admin("✅ Бот остановлен", level="warning")

# --- Main Execution Block ---
if __name__ == '__main__':
    # При запуске напрямую используем функцию start_bot
    start_bot()
