# Этот код предназначен для файла bot_globals.py

import telebot
import threading
import time
import logging
import os
import json
from collections import defaultdict
import collections  # Added for collections.deque

# Import configuration variables
from config import (
    BOT_TOKEN,
    MEDIA_GROUP_DELAY,
    AUDIO_VIDEO_GROUP_DELAY,
    FORWARD_BATCH_DELAY,
    PROCESS_BUFFER_DELAY,
    TELEGRAM_CONNECTION_TIMEOUT,
    TELEGRAM_READ_TIMEOUT,
    TELEGRAM_REQUEST_TIMEOUT,
)

# --- Семафоры для разделения LLM-запросов ---
LLM_PRIVATE_SEM = threading.Semaphore(3)   # всегда ≥3 слота под private
LLM_PODCAST_SEM = threading.Semaphore(1)   # подкасты не забьют канал

# --- НАСТРОЙКА ЛОГГИРОВАНИЯ --- # <--- ДОБАВИТЬ ВЕСЬ ЭТОТ БЛОК
# Определяем имя файла лога
log_file_name = "bot_activity.txt"
# Определяем путь к файлу лога (в той же папке, что и скрипт)
log_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), log_file_name)

# Создаем или получаем логгер
logger = logging.getLogger("bot_logger")
logger.setLevel(logging.INFO)  # Уменьшаем уровень детализации с DEBUG на INFO

# Создаем обработчик для записи в файл с ротацией
from logging.handlers import TimedRotatingFileHandler
import gzip
import shutil
import threading

class CompressedTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Обработчик с автоматическим сжатием старых логов в отдельном потоке."""

    def _compress_file_async(self, sfn):
        """Сжимает файл в отдельном потоке, чтобы не блокировать GIL."""
        try:
            if not sfn.endswith('.gz') and os.path.exists(sfn):
                with open(sfn, 'rb') as f_in:
                    with gzip.open(sfn + '.gz', 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                os.remove(sfn)  # Удаляем несжатый файл
        except Exception as e:
            # Логируем ошибку, но не прерываем работу
            print(f"Ошибка при сжатии лог-файла {sfn}: {e}")

    def doRollover(self):
        """Переопределяем метод для добавления асинхронного сжатия."""
        super().doRollover()

        # Сжимаем предыдущий лог-файл в отдельном потоке
        if self.backupCount > 0:
            for i in range(self.backupCount - 1, 0, -1):
                sfn = self.rotation_filename("%s.%d" % (self.baseFilename, i))
                dfn = self.rotation_filename("%s.%d" % (self.baseFilename, i + 1))
                if os.path.exists(sfn):
                    if os.path.exists(dfn):
                        os.remove(dfn)
                    # Запускаем сжатие в отдельном потоке, чтобы не блокировать основной поток
                    if not sfn.endswith('.gz'):
                        compress_thread = threading.Thread(
                            target=self._compress_file_async, 
                            args=(sfn,),
                            daemon=True
                        )
                        compress_thread.start()

file_handler = CompressedTimedRotatingFileHandler(
    log_file_path,
    when='H',  # Ротация каждый час
    interval=6,  # Каждые 6 часов
    backupCount=20,  # Хранить 20 архивных файлов (5 дней)
    encoding="utf-8"
)
file_handler.setLevel(logging.INFO)  # Логировать INFO и выше в файл

# Создаем обработчик для вывода в консоль
console_handler = logging.StreamHandler()
console_handler.setLevel(
    logging.WARNING  # Предупреждения, ошибки и критические сообщения в терминал
)

# Создаем форматтеры - подробный для файла, краткий для консоли
file_formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
)

# Фильтр для исключения спам-сообщений API из логов
class APISpamFilter(logging.Filter):
    def filter(self, record):
        message = record.getMessage()
        # Исключаем спам-сообщения API и другие частые сообщения
        spam_patterns = [
            "Using Official Gemini API for model:",
            "Attempting API call to",
            "Attempt to",
            "Payload for gemini",
            "Payload for",
            "Successfully received response from",
            "API call successful. Text length:",
            "with key ending",
            "response received successfully",
            "Routing model:",
            "GeminiRouter",
            "GeminiOfficial",
            "GeminiViaVoid",
            "received forwarded text",
            "adding to forward batch",
            "received",
            "duration:",
            "forwarded:",
            "from:",
            "'rint' tag stripped",
            "Original:",
            "New:",
            "Sent new status message",
            "Error sending new status",
            "Error updating status message",
            "Error updating error message",
            "User",
            "promoted to admin in group",
            "via /adme command"
        ]

        for pattern in spam_patterns:
            if pattern in message:
                return False  # Не логировать это сообщение

        return True  # Логировать все остальные сообщения

# Простой и компактный форматтер для консоли - только тип ошибки и сообщение
class CompactConsoleFormatter(logging.Formatter):
    level_names = {
        logging.ERROR: "❌ Ошибка",
        logging.CRITICAL: "💀 Критично"
    }

    def format(self, record):
        level_name = self.level_names.get(record.levelno, record.levelname)
        # Убираем временные метки для краткости
        return f"{level_name}: {record.getMessage()}"

console_formatter = CompactConsoleFormatter()

file_handler.setFormatter(file_formatter)
console_handler.setFormatter(console_formatter)

# Добавляем фильтр для исключения спам-сообщений API
api_spam_filter = APISpamFilter()
file_handler.addFilter(api_spam_filter)

# Добавляем обработчики к логгеру
# Проверяем, чтобы не добавлять обработчики многократно, если модуль перезагружается
if not logger.handlers:
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

# Отключаем verbose логирование для httpx (HTTP запросы)
logging.getLogger("httpx").setLevel(logging.WARNING)
# --- КОНЕЦ БЛОКА НАСТРОЙКИ ЛОГГИРОВАНИЯ ---

# --- Настройка таймаутов для Telegram API ---
# Настраиваем telebot.apihelper с кастомными таймаутами для предотвращения ошибок таймаута
telebot.apihelper.CONNECT_TIMEOUT = TELEGRAM_CONNECTION_TIMEOUT
telebot.apihelper.READ_TIMEOUT = TELEGRAM_READ_TIMEOUT

# --- Global Bot Object ---
bot = telebot.TeleBot(BOT_TOKEN)
bot_start_time = time.time()  # Record bot start time

def safe_bot_api_call(func, *args, max_retries=3, base_delay=1, **kwargs):
    """
    Безопасный вызов Telegram Bot API с retry механизмом для обработки ошибок 429 (Too Many Requests)

    Args:
        func: Функция bot API для вызова (например, bot.edit_message_text)
        *args: Позиционные аргументы для функции
        max_retries: Максимальное количество попыток (по умолчанию 3)
        base_delay: Базовая задержка в секундах (по умолчанию 1)
        **kwargs: Именованные аргументы для функции

    Returns:
        Результат вызова функции или None в случае неудачи
    """
    import re
    for attempt in range(max_retries + 1):
        try:
            return func(*args, **kwargs)
        except telebot.apihelper.ApiTelegramException as e:
            if "429" in str(e) and "Too Many Requests" in str(e):
                if attempt < max_retries:
                    # Экспоненциальная задержка: 1s, 2s, 4s, 8s...
                    delay = base_delay * (2 ** attempt)

                    # Извлекаем время ожидания из ответа Telegram, если доступно
                    retry_after_match = re.search(r'retry after (\d+)', str(e))
                    if retry_after_match:
                        telegram_delay = int(retry_after_match.group(1))
                        delay = max(delay, telegram_delay)  # Используем большее значение

                    log_admin(f"Rate limit hit (429), retrying in {delay}s (attempt {attempt + 1}/{max_retries + 1})", level="warning")
                    time.sleep(delay)
                    continue
                else:
                    log_admin(f"Rate limit hit (429), max retries ({max_retries}) exceeded", level="error")
                    return None
            else:
                # Для других ошибок API не делаем retry
                raise e
        except Exception as e:
            # Для неожиданных ошибок не делаем retry
            raise e

    return None

# Создаем обертку для bot с безопасными вызовами API
class SafeBotWrapper:
    def __init__(self, original_bot):
        self._bot = original_bot

    # Методы API, которые могут вызывать ошибки 429 и нуждаются в retry
    SAFE_METHODS = {
        'edit_message_text',
        'edit_message_reply_markup',
        'send_message',
        'send_photo',
        'send_audio',
        'send_document',
        'send_video',
        'send_voice',
        'send_chat_action',
        'answer_callback_query',
        'delete_message',
        'set_message_reaction'  # Добавлен метод для установки реакций
    }

    def __getattr__(self, name):
        attr = getattr(self._bot, name)
        # Если это один из методов API, который может вызывать 429, оборачиваем его
        if name in self.SAFE_METHODS and callable(attr):
            def safe_wrapper(*args, **kwargs):
                # Специальная обработка для методов отправки сообщений - конвертируем markdown в HTML
                from utils import convert_markdown_to_html

                if name == 'send_message':
                    # Обрабатываем текст в args
                    if len(args) >= 2 and isinstance(args[1], str):
                        converted_text = convert_markdown_to_html(args[1])
                        args = list(args)
                        args[1] = converted_text
                        args = tuple(args)
                    # Обрабатываем текст в kwargs
                    if 'text' in kwargs and isinstance(kwargs['text'], str):
                        kwargs['text'] = convert_markdown_to_html(kwargs['text'])

                elif name in ['send_photo', 'send_audio', 'send_document', 'send_video', 'send_voice']:
                    # Обрабатываем caption в kwargs
                    if 'caption' in kwargs and isinstance(kwargs['caption'], str):
                        kwargs['caption'] = convert_markdown_to_html(kwargs['caption'])

                elif name == 'edit_message_text':
                    # Обрабатываем текст в args
                    if len(args) >= 1 and isinstance(args[0], str):
                        converted_text = convert_markdown_to_html(args[0])
                        args = list(args)
                        args[0] = converted_text
                        args = tuple(args)
                    # Обрабатываем текст в kwargs
                    if 'text' in kwargs and isinstance(kwargs['text'], str):
                        kwargs['text'] = convert_markdown_to_html(kwargs['text'])

                return safe_bot_api_call(attr, *args, **kwargs)
            return safe_wrapper
        # Для всех остальных атрибутов возвращаем как есть
        return attr

# Заменяем bot на безопасную обертку
bot = SafeBotWrapper(bot)

# --- Global State Variables ---
# ЭТАП 2: Заменяем defaultdict на OrderedDict с ограничением размера
from collections import OrderedDict

class LimitedOrderedDict(OrderedDict):
    """OrderedDict с ограничением размера для user_conversations."""
    def __init__(self, max_size=100):
        super().__init__()
        self.max_size = max_size

    def __setitem__(self, key, value):
        super().__setitem__(key, value)
        # Удаляем старые записи, если превышен лимит
        while len(self) > self.max_size:
            oldest_key = next(iter(self))
            del self[oldest_key]

user_conversations = LimitedOrderedDict(max_size=100)  # Ограничено 100 записями
user_conversations_lock = threading.Lock()  # Lock for user_conversations

user_model_override = {}  # Stores model override setting per user
chat_model_override = {}  # Stores model override setting per chat (for /gemini command)
admin_logging_enabled = (
    set()
)  # Stores chat IDs where admin logging is enabled (МОЖНО ОСТАВИТЬ ДЛЯ ОБРАТНОЙ СОВМЕСТИМОСТИ ИЛИ УДАЛИТЬ, ЕСЛИ НЕ ИСПОЛЬЗУЕТСЯ ДЛЯ ФИЛЬТРАЦИИ)



# --- User Settings ---
# Структура для хранения пользовательских настроек
# По умолчанию:
# - image_model: 'gpt-image-1' (модель для генерации изображений)
# - image_quality: 'high' (качество изображений: 'standard'/'high')
# - save_history: True (сохранять историю запросов)
# - language: 'ru' (язык интерфейса: 'ru'/'en')
# - tag_processing: 'auto_remove' (обработка тегов: 'auto_remove'/'keep')
USER_SETTINGS_DEFAULT = {
    "image_model": "gpt-image-1",
    "image_quality": "high",
    "save_history": True,
    "language": "ru",
    "tag_processing": "auto_remove",
    # flags for one-time feature notifications
    "notif_search_shown": False,
    "notif_audio_summary_shown": False,
    "notif_voice_answer_shown": False,
    # auto podcast generation after research
    "diana_auto_podcast": False,
    # ultrathink mode for enhanced reasoning
    "ultrathink_enabled": False,
    # ultrapro mode for maximum enhanced reasoning
    "ultrapro_enabled": False,
    # custom user rules for system prompt
    "custom_rules": [],
}

# Словарь для хранения настроек пользователей
user_settings = defaultdict(lambda: USER_SETTINGS_DEFAULT.copy())
user_settings_lock = threading.Lock()  # Lock для безопасного доступа к user_settings

# Путь к файлу для сохранения настроек (теперь в bot_data.json)
BOT_DATA_FILE = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "bot_data.json"
)
USER_SETTINGS_FILE = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "user_settings.json"
)

telegraph_client = None  # Initialized later in main.py via setup_telegraph

# State for callback queries (e.g., summarization toggle)
message_states = {}
message_states_lock = threading.Lock()

# State for media group processing (photos)
media_groups = defaultdict(
    lambda: {
        "photos": [],
        "caption": None,
        "timer": None,
        "chat_id": None,
        "user_id": None,
        "processed": False,
        "thinking_message_id": None,
        "message_ids": set(),
        "streaming_message_id": None,
        "timestamp": time.time(),  # TTL для автоматической очистки
    }
)
media_group_lock = threading.Lock()

# State for audio/video group processing
audio_video_groups = defaultdict(
    lambda: {
        "files": [],
        "timer": None,
        "chat_id": None,
        "processed": False,
        "status_message_id": None,
        "has_video": False,
        "is_forwarded": False,
        "forwarder_name": "Пользователь",
        "timestamp": time.time(),  # TTL для автоматической очистки
    }
)
audio_video_group_lock = threading.Lock()

# State for buffering forwarded text messages
user_forward_batch = defaultdict(
    lambda: {"messages": [], "timer": None, "chat_id": None, "timestamp": time.time()}
)
user_forward_batch_lock = threading.Lock()

# State for pending file content waiting for a user query
user_pending_file = {}
user_pending_file_lock = threading.Lock()

# Stores the last raw response from the model for export commands (/txt, /html, /py)
user_last_response = {}
user_last_response_lock = threading.Lock()

# State for buffering quick successive text/photo messages
user_request_buffer = defaultdict(
    lambda: {"messages": [], "timer": None, "chat_id": None, "timestamp": time.time()}
)
user_request_buffer_lock = threading.Lock()

# State for tracking chats where multiple messages are being sent (to prevent interruptions)
multiple_messages_sending = {}  # chat_id -> True/False
multiple_messages_sending_lock = threading.Lock()

# Queue for messages received while sending multiple messages (to process them after)
pending_messages_queue = defaultdict(list)  # chat_id -> [list of message objects]
pending_messages_queue_lock = threading.Lock()



# --- /chatme System - User Message Interception ---
# Dictionary to track which users have chatme mode active
# Key: user_id, Value: True/False
chatme_active_users = {}
chatme_active_lock = threading.Lock()

# Queue for intercepted messages waiting for admin response
# Key: unique_message_key (f"{chat_id}_{message_id}_{timestamp}")
# Value: dict {
#   'original_message': telebot.types.Message,
#   'user_info': dict,
#   'chat_info': dict,
#   'timer': threading.Timer,
#   'status': str ('waiting', 'admin_responding', 'ai_processing', 'completed'),
#   'admin_notification_id': int (message ID sent to admin),
#   'timestamp': float
# }
intercepted_messages = {}
intercepted_messages_lock = threading.Lock()

# Admin response state - tracks which message admin is responding to
# Key: admin_user_id
# Value: dict {
#   'responding_to_key': str (key from intercepted_messages),
#   'timestamp': float,
#   'notification_message_id': int
# }
admin_response_state = {}
admin_response_state_lock = threading.Lock()

# Chatme response state - tracks users waiting to send response via button
# Key: user_id
# Value: dict {
#   'target_chat_id': int (chat where to send response),
#   'original_user_id': int (user who sent /chatme),
#   'timestamp': float,
#   'callback_message_id': int (message ID with the button)
# }
chatme_response_state = {}
chatme_response_state_lock = threading.Lock()

# --- Forwarded Audio/Video Queue System ---
forwarded_audio_queue = collections.deque()
forwarded_audio_queue_lock = threading.Lock()
forwarded_audio_processor_active = threading.Event()

# Research podcast data structures removed - old deep research feature has been removed

# --- Podcast Themes Storage ---
# Global dictionary to store podcast themes temporarily
# Key: f"{chat_id}_{user_id}_{timestamp}"
# Value: dict {
#   'theme': str (podcast theme),
#   'custom_title': str (custom title),
#   'hours': int (hours to collect messages),
#   'hours_specified': bool (if user explicitly specified hours),
#   'podcast_type': str (type of podcast),
#   'user_nickname': str (user nickname),
#   'timestamp': float (when request was created),
#   'original_message_id': int (original message ID)
# }
podcast_themes = {}
podcast_themes_lock = threading.Lock()

# --- Podcast Queue System REMOVED ---
# Podcast queue system has been removed. Podcasts now run directly in parallel threads.
# Only FFmpeg queue remains in utils.py for preventing server overload.


# --- User Settings Functions ---
def save_user_settings():
    """Сохраняет настройки пользователей в объединенный файл bot_data.json"""
    try:
        # Импортируем блокировку из admin_system для синхронизации
        from admin_system import data_lock

        with data_lock:  # Используем общую блокировку для предотвращения конфликтов
            # Загружаем существующие данные
            bot_data = {}
            if os.path.exists(BOT_DATA_FILE):
                with open(BOT_DATA_FILE, "r", encoding="utf-8") as f:
                    bot_data = json.load(f)

            with user_settings_lock:
                # Конвертируем defaultdict в обычный dict для сериализации
                settings_to_save = {
                    str(user_id): settings for user_id, settings in user_settings.items()
                }

                # Обновляем секцию user_settings в bot_data
                bot_data["user_settings"] = settings_to_save

            # Сохраняем обновленные данные
            with open(BOT_DATA_FILE, "w", encoding="utf-8") as f:
                json.dump(bot_data, f, ensure_ascii=False, indent=2)

        log_admin(
            f"Настройки пользователей сохранены в {BOT_DATA_FILE}", level="info"
        )
        return True
    except Exception as e:
        log_admin(f"Ошибка при сохранении настроек пользователей: {e}", level="error")
        return False


def load_user_settings():
    """Загружает настройки пользователей из объединенного файла bot_data.json или legacy файла"""
    loaded_settings = {}

    # Сначала пробуем загрузить из объединенного файла
    if os.path.exists(BOT_DATA_FILE):
        try:
            with open(BOT_DATA_FILE, "r", encoding="utf-8") as f:
                bot_data = json.load(f)
                loaded_settings = bot_data.get("user_settings", {})
        except Exception as e:
            log_admin(f"Ошибка при загрузке настроек из {BOT_DATA_FILE}: {e}", level="error")

    # Если настроек нет в объединенном файле, пробуем legacy файл
    if not loaded_settings and os.path.exists(USER_SETTINGS_FILE):
        try:
            with open(USER_SETTINGS_FILE, "r", encoding="utf-8") as f:
                loaded_settings = json.load(f)

            # Мигрируем данные в объединенный файл
            log_admin("Мигрируем настройки пользователей в объединенный файл", level="info")
            save_user_settings()

            # Создаем backup legacy файла
            backup_file = f"{USER_SETTINGS_FILE}.backup"
            os.rename(USER_SETTINGS_FILE, backup_file)
            log_admin(f"Legacy файл {USER_SETTINGS_FILE} сохранен как {backup_file}", level="info")

        except Exception as e:
            log_admin(f"Ошибка при загрузке legacy настроек: {e}", level="error")

    if not loaded_settings:
        log_admin(
            f"Файл настроек пользователей не найден. Будут использованы настройки по умолчанию.",
            level="info",
        )
        return False

    try:
        with user_settings_lock:
            for user_id_str, settings in loaded_settings.items():
                # Преобразуем строковый ключ обратно в int
                try:
                    user_id = int(user_id_str)
                    # Обновляем только те настройки, которые есть в загруженных данных
                    user_settings[user_id] = (
                        USER_SETTINGS_DEFAULT.copy()
                    )  # Начинаем с настроек по умолчанию
                    user_settings[user_id].update(
                        settings
                    )  # Обновляем загруженными данными
                except ValueError:
                    log_admin(
                        f"Некорректный ID пользователя в файле настроек: {user_id_str}",
                        level="info",
                    )

        log_admin(
            f"Настройки пользователей загружены ({len(loaded_settings)} пользователей)", level="info"
        )
        return True
    except Exception as e:
        log_admin(f"Ошибка при обработке настроек пользователей: {e}", level="error")
        return False


def get_user_setting(user_id, setting_name):
    """Получает значение настройки для пользователя"""
    with user_settings_lock:
        user_config = user_settings[user_id]
        return user_config.get(setting_name, USER_SETTINGS_DEFAULT.get(setting_name))


def set_user_setting(user_id, setting_name, value):
    """Устанавливает значение настройки для пользователя"""
    if setting_name not in USER_SETTINGS_DEFAULT:
        return False

    with user_settings_lock:
        user_settings[user_id][setting_name] = value

    # Асинхронное сохранение можно добавить здесь, если нужно
    # Или сохранять периодически или при выключении бота
    save_user_settings()
    return True


# --- One-Time Notification Helper ---
NOTIFICATION_TEXTS = {
    "search": (
        "notif_search_shown",
        "🌐 Вы только что воспользовались поиском в интернете.\n<i>Данное уведомление больше не будет появляться</i>",
    ),
    "audio_summary": (
        "notif_audio_summary_shown",
        "🎙️ Вы только что воспользовались функцией создания сводок голосовых сообщений.\nТакже в боте есть функция <b>ответа на ваши вопросы</b> от ваших голосовых. Просто запишите его в чат.\n<i>Данное уведомление больше не будет появляться</i>",
    ),
    "voice_answer": (
        "notif_voice_answer_shown",
        "🗣️ Вы только что воспользовались функцией ответов на голосовые сообщения.\nТакже в боте есть функция <b>краткой сводки</b> голосовых сообщений. Для этого его нужно переслать.\n<i>Данное уведомление больше не будет появляться</i>",
    ),
}


def send_one_time_notification(user_id: int, chat_id: int, notif_key: str):
    """Send one-time notification in private chat if not already shown."""
    if chat_id != user_id:
        return
    data = NOTIFICATION_TEXTS.get(notif_key)
    if not data:
        return
    setting_name, text = data
    if not get_user_setting(user_id, setting_name):
        try:
            bot.send_message(chat_id, text, parse_mode="HTML")
        except Exception as e:
            log_admin(
                f"Failed to send one-time notification '{notif_key}' to user {user_id}: {e}",
                level="error",
            )
        set_user_setting(user_id, setting_name, True)


# --- Logging Function ---
def log_admin(log_message, level="info"):  # <--- ИЗМЕНИТЬ СИГНАТУРУ ФУНКЦИИ
    """
    Записывает сообщение в лог с указанным уровнем.
    Уровни: "debug", "info", "warning", "error", "critical".
    """
    if level.lower() == "debug":
        logger.debug(log_message)
    elif level.lower() == "info":
        logger.info(log_message)
    elif level.lower() == "warning":
        logger.warning(log_message)
    elif level.lower() == "error":
        logger.error(log_message)
    elif level.lower() == "critical":
        logger.critical(log_message)
    else:
        logger.info(log_message)  # По умолчанию INFO

    # Старую логику с admin_logging_enabled можно оставить, если она нужна для каких-то специфических
    # уведомлений администраторам в чат, но для основного логирования она больше не нужна.
    # Если admin_logging_enabled использовался только для вывода в консоль, его можно убрать,
    # так как новый логгер уже выводит в консоль.
    # Для примера, я оставлю старую проверку, но она будет печатать в консоль ДУБЛИРУЮЩЕЕ сообщение, если активна.
    # Рекомендуется ее убрать или переделать для отправки сообщений администраторам в Telegram, если это нужно.
    if (
        admin_logging_enabled
    ):  # Эта часть теперь может быть избыточной или требовать переосмысления
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # print(f"[DEPRECATED admin log {timestamp}] {log_message}") # Пометим как устаревшее


# --- Podcast Queue Management Functions REMOVED ---
# All podcast queue management functions have been removed.
# Podcasts now run directly in parallel threads without queuing.


# --- Memory Optimization: TTL and Cleanup Functions ---
# Настройки TTL для автоматической очистки
MEDIA_GROUP_TTL = 7200  # 2 часа для media groups (больше времени жизни)
AUDIO_VIDEO_GROUP_TTL = 7200  # 2 часа для audio/video groups
USER_FORWARD_BATCH_TTL = 1800  # 30 минут для forward batches
USER_REQUEST_BUFFER_TTL = 1800  # 30 минут для request buffers (увеличено)
MAX_DICT_SIZE = 5000  # Максимальный размер для defaultdict (увеличено для больших буферов)


def cleanup_expired_media_groups():
    """Очистка устаревших media groups по TTL."""
    current_time = time.time()
    expired_keys = []

    with media_group_lock:
        for group_id, group_data in media_groups.items():
            # Проверяем TTL
            if current_time - group_data.get("timestamp", 0) > MEDIA_GROUP_TTL:
                expired_keys.append(group_id)
            # Также очищаем уже обработанные группы старше 5 минут
            elif group_data.get("processed", False) and current_time - group_data.get("timestamp", 0) > 300:
                expired_keys.append(group_id)

        # Удаляем устаревшие записи
        for key in expired_keys:
            try:
                # Отменяем таймер, если он есть
                if media_groups[key].get("timer"):
                    media_groups[key]["timer"].cancel()
                del media_groups[key]
            except KeyError:
                pass  # Уже удалено

    if expired_keys:
        log_admin(f"Очищено {len(expired_keys)} устаревших media groups", level="debug")

    return len(expired_keys)


def cleanup_expired_audio_video_groups():
    """Очистка устаревших audio/video groups по TTL."""
    current_time = time.time()
    expired_keys = []

    with audio_video_group_lock:
        for group_id, group_data in audio_video_groups.items():
            # Проверяем TTL
            if current_time - group_data.get("timestamp", 0) > AUDIO_VIDEO_GROUP_TTL:
                expired_keys.append(group_id)
            # Также очищаем уже обработанные группы старше 10 минут
            elif group_data.get("processed", False) and current_time - group_data.get("timestamp", 0) > 600:
                expired_keys.append(group_id)

        # Удаляем устаревшие записи
        for key in expired_keys:
            try:
                # Отменяем таймер, если он есть
                if audio_video_groups[key].get("timer"):
                    audio_video_groups[key]["timer"].cancel()
                del audio_video_groups[key]
            except KeyError:
                pass  # Уже удалено

    if expired_keys:
        log_admin(f"Очищено {len(expired_keys)} устаревших audio/video groups", level="debug")

    return len(expired_keys)


def cleanup_expired_user_buffers():
    """Очистка устаревших пользовательских буферов."""
    current_time = time.time()
    cleaned_count = 0

    # Очистка forward batches
    expired_keys = []
    with user_forward_batch_lock:
        for user_id, batch_data in user_forward_batch.items():
            if current_time - batch_data.get("timestamp", current_time) > USER_FORWARD_BATCH_TTL:
                expired_keys.append(user_id)

        for key in expired_keys:
            try:
                if user_forward_batch[key].get("timer"):
                    user_forward_batch[key]["timer"].cancel()
                del user_forward_batch[key]
                cleaned_count += 1
            except KeyError:
                pass

    # Очистка request buffers
    expired_keys = []
    with user_request_buffer_lock:
        for user_id, buffer_data in user_request_buffer.items():
            if current_time - buffer_data.get("timestamp", current_time) > USER_REQUEST_BUFFER_TTL:
                expired_keys.append(user_id)

        for key in expired_keys:
            try:
                if user_request_buffer[key].get("timer"):
                    user_request_buffer[key]["timer"].cancel()
                del user_request_buffer[key]
                cleaned_count += 1
            except KeyError:
                pass

    if cleaned_count > 0:
        log_admin(f"Очищено {cleaned_count} устаревших пользовательских буферов", level="debug")

    return cleaned_count


def limit_dict_sizes():
    """Ограничение размеров defaultdict для предотвращения неконтролируемого роста."""
    limited_count = 0

    # Ограничиваем размер user_conversations
    with user_conversations_lock:
        if len(user_conversations) > MAX_DICT_SIZE:
            # Удаляем самые старые записи (по ключам)
            sorted_keys = sorted(user_conversations.keys())
            keys_to_remove = sorted_keys[:-MAX_DICT_SIZE]
            for key in keys_to_remove:
                del user_conversations[key]
                limited_count += 1

    # Ограничиваем размер user_settings
    with user_settings_lock:
        if len(user_settings) > MAX_DICT_SIZE:
            # Удаляем самые старые записи (по ключам)
            sorted_keys = sorted(user_settings.keys())
            keys_to_remove = sorted_keys[:-MAX_DICT_SIZE]
            for key in keys_to_remove:
                del user_settings[key]
                limited_count += 1

    # Ограничиваем размер других словарей
    if len(user_model_override) > MAX_DICT_SIZE:
        sorted_keys = sorted(user_model_override.keys())
        keys_to_remove = sorted_keys[:-MAX_DICT_SIZE]
        for key in keys_to_remove:
            del user_model_override[key]
            limited_count += 1

    if len(chat_model_override) > MAX_DICT_SIZE:
        sorted_keys = sorted(chat_model_override.keys())
        keys_to_remove = sorted_keys[:-MAX_DICT_SIZE]
        for key in keys_to_remove:
            del chat_model_override[key]
            limited_count += 1

    if limited_count > 0:
        log_admin(f"Ограничен размер словарей: удалено {limited_count} записей", level="debug")

    return limited_count


def cleanup_old_conversations():
    """ЭТАП 2: Очистка старых разговоров из user_conversations."""
    cleaned_count = 0

    try:
        with user_conversations_lock:
            # LimitedOrderedDict автоматически удаляет старые записи,
            # но мы можем дополнительно очистить неактивные разговоры
            current_time = time.time()
            keys_to_remove = []

            for user_id, conversation in user_conversations.items():
                # Удаляем разговоры старше 24 часов без активности
                if conversation and len(conversation) > 0:
                    # Проверяем время последнего сообщения (если есть timestamp)
                    last_message = conversation[-1] if conversation else None
                    if isinstance(last_message, dict) and 'timestamp' in last_message:
                        if current_time - last_message['timestamp'] > 86400:  # 24 часа
                            keys_to_remove.append(user_id)
                    # Если разговор слишком длинный (>50 сообщений), оставляем только последние 30

            # Удаляем старые разговоры
            for user_id in keys_to_remove:
                del user_conversations[user_id]
                cleaned_count += 1

            if cleaned_count > 0:
                log_admin(f"Очистка разговоров: удалено {cleaned_count} записей/сообщений", level="debug")

    except Exception as e:
        log_admin(f"Ошибка при очистке разговоров: {e}", level="warning")

    return cleaned_count


def cleanup_all_expired_data():
    """Комплексная очистка всех устаревших данных."""
    total_cleaned = 0

    try:
        total_cleaned += cleanup_expired_media_groups()
        total_cleaned += cleanup_expired_audio_video_groups()
        total_cleaned += cleanup_expired_user_buffers()
        total_cleaned += cleanup_old_conversations()  # ЭТАП 2: Добавлена очистка разговоров
        total_cleaned += cleanup_old_podcast_themes()  # Очистка старых тем подкастов
        total_cleaned += limit_dict_sizes()

        if total_cleaned > 0:
            log_admin(f"Общая очистка завершена: удалено {total_cleaned} записей", level="info")

            # Принудительная сборка мусора после очистки
            try:
                import gc
                collected = gc.collect()
                log_admin(f"Сборка мусора после очистки: освобождено {collected} объектов", level="debug")
            except Exception as e:
                log_admin(f"Ошибка при сборке мусора: {e}", level="warning")

        return total_cleaned

    except Exception as e:
        log_admin(f"Ошибка при комплексной очистке данных: {e}", level="error")
        return 0


# Функция get_memory_stats удалена (модуль управления памятью удален)


def cleanup_old_podcast_themes():
    """Clean up podcast themes older than 10 minutes"""
    current_time = time.time()
    keys_to_remove = []

    with podcast_themes_lock:
        for key, data in podcast_themes.items():
            if current_time - data['timestamp'] > 600:  # 10 minutes
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del podcast_themes[key]

    if keys_to_remove:
        log_admin(f"Cleaned up {len(keys_to_remove)} old podcast themes")

    return len(keys_to_remove)

































def process_pending_messages_for_chat(chat_id):
    """
    Обрабатывает накопленные сообщения для конкретного чата после завершения отправки множественных сообщений.
    Добавляет небольшую задержку для естественности и обрабатывает сообщения по порядку.
    
    Args:
        chat_id: ID чата для обработки накопленных сообщений
    """
    import time
    import threading
    
    def process_messages():
        try:
            # Небольшая задержка перед обработкой накопленных сообщений (для естественности)
            time.sleep(1.5)
            
            # Получаем накопленные сообщения для этого чата
            messages_to_process = []
            with pending_messages_queue_lock:
                if chat_id in pending_messages_queue and pending_messages_queue[chat_id]:
                    messages_to_process = pending_messages_queue[chat_id].copy()
                    pending_messages_queue[chat_id].clear()  # Очищаем очередь
            
            if not messages_to_process:
                return
            
            log_admin(f"Processing {len(messages_to_process)} pending messages for chat {chat_id}", level="debug")
            
            # Обрабатываем сообщения по порядку
            for i, message in enumerate(messages_to_process):
                try:
                    log_admin(f"Processing pending message {i+1}/{len(messages_to_process)} for chat {chat_id}", level="debug")
                    
                    # Вызываем обработчик сообщений напрямую через глобальную переменную
                    if hasattr(process_pending_messages_for_chat, '_message_handler'):
                        process_pending_messages_for_chat._message_handler(message)
                    else:
                        log_admin(f"Message handler not set for pending messages processing", level="error")
                        break
                    
                    # Небольшая задержка между обработкой сообщений (чтобы не спамить)
                    if i < len(messages_to_process) - 1:  # Не ждем после последнего сообщения
                        time.sleep(0.5)
                        
                except Exception as e:
                    log_admin(f"Error processing pending message {i+1} for chat {chat_id}: {e}", level="error")
                    continue  # Продолжаем обработку остальных сообщений
            
            log_admin(f"Completed processing {len(messages_to_process)} pending messages for chat {chat_id}", level="debug")
            
        except Exception as e:
            log_admin(f"Error in process_pending_messages_for_chat for chat {chat_id}: {e}", level="error")
    
    # Запускаем обработку в отдельном потоке чтобы не блокировать основной поток
    thread = threading.Thread(target=process_messages, daemon=True)
    thread.start()

# Глобальная переменная для хранения ссылки на обработчик сообщений
_message_handler_ref = None

def set_message_handler(handler_func):
    """
    Устанавливает ссылку на функцию обработки сообщений для использования в process_pending_messages_for_chat.
    Должна вызываться из handlers.py при инициализации.
    """
    global _message_handler_ref
    _message_handler_ref = handler_func
    process_pending_messages_for_chat._message_handler = handler_func
    log_admin("Message handler set for pending messages processing", level="debug")


# --- ЭТАП 2: КЭШИРОВАНИЕ BOT.GET_ME() И RETRY МЕХАНИЗМ ---

# Кэш для bot.get_me() с TTL
_bot_info_cache = {
    'data': None,
    'timestamp': 0,
    'ttl': 3600  # 1 час в секундах
}
_bot_info_cache_lock = threading.Lock()

def get_bot_info(force_refresh=False):
    """
    Кэшированная версия bot.get_me() с retry механизмом и обработкой исключений.

    Args:
        force_refresh: Принудительно обновить кэш, игнорируя TTL

    Returns:
        dict: Информация о боте или None в случае ошибки
    """
    current_time = time.time()

    with _bot_info_cache_lock:
        # Проверяем кэш
        if not force_refresh and _bot_info_cache['data'] is not None:
            cache_age = current_time - _bot_info_cache['timestamp']
            if cache_age < _bot_info_cache['ttl']:
                log_admin(f"Используем кэшированную информацию о боте (возраст: {cache_age:.1f}s)", level="debug")
                return _bot_info_cache['data']

        # Кэш устарел или принудительное обновление
        log_admin("Обновляем информацию о боте через API", level="debug")

        # Retry механизм с экспоненциальной задержкой
        max_retries = 3
        base_delay = 2

        for attempt in range(max_retries + 1):
            try:
                # Используем безопасный API вызов с таймаутами
                bot_info = bot._bot.get_me()  # Обращаемся к оригинальному bot объекту

                if bot_info:
                    # Конвертируем в dict для кэширования
                    bot_data = {
                        'id': bot_info.id,
                        'username': bot_info.username,
                        'first_name': bot_info.first_name,
                        'is_bot': bot_info.is_bot,
                        'can_join_groups': getattr(bot_info, 'can_join_groups', None),
                        'can_read_all_group_messages': getattr(bot_info, 'can_read_all_group_messages', None),
                        'supports_inline_queries': getattr(bot_info, 'supports_inline_queries', None)
                    }

                    # Обновляем кэш
                    _bot_info_cache['data'] = bot_data
                    _bot_info_cache['timestamp'] = current_time

                    log_admin(f"Информация о боте успешно получена: @{bot_data['username']} (ID: {bot_data['id']})", level="info")
                    return bot_data

            except telebot.apihelper.ApiTelegramException as e:
                error_msg = str(e)
                if "429" in error_msg and "Too Many Requests" in error_msg:
                    # Rate limit - используем экспоненциальную задержку
                    if attempt < max_retries:
                        delay = base_delay * (2 ** attempt)
                        log_admin(f"Rate limit при получении bot.get_me(), ждем {delay}s (попытка {attempt + 1}/{max_retries + 1})", level="warning")
                        time.sleep(delay)
                        continue
                    else:
                        log_admin(f"Rate limit при получении bot.get_me(), исчерпаны попытки", level="error")
                        break
                else:
                    log_admin(f"Ошибка Telegram API при получении bot.get_me(): {error_msg}", level="error")
                    break

            except Exception as e:
                log_admin(f"Неожиданная ошибка при получении bot.get_me() (попытка {attempt + 1}): {e}", level="error")
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt)
                    log_admin(f"Повторная попытка через {delay}s", level="warning")
                    time.sleep(delay)
                    continue
                else:
                    break

        # Если все попытки неудачны, возвращаем кэшированные данные (если есть) или fallback
        if _bot_info_cache['data'] is not None:
            log_admin("Используем устаревшие кэшированные данные о боте из-за ошибок API", level="warning")
            return _bot_info_cache['data']

        # Fallback значения если кэш пуст
        log_admin("Используем fallback значения для информации о боте", level="error")
        fallback_data = {
            'id': 0,  # Будет заменено при первом успешном вызове
            'username': 'unknown_bot',
            'first_name': 'Bot',
            'is_bot': True,
            'can_join_groups': True,
            'can_read_all_group_messages': False,
            'supports_inline_queries': False
        }

        return fallback_data

def get_bot_id():
    """
    Получает ID бота из кэшированной информации.

    Returns:
        int: ID бота или 0 в случае ошибки
    """
    try:
        bot_info = get_bot_info()
        return bot_info.get('id', 0) if bot_info else 0
    except Exception as e:
        log_admin(f"Ошибка при получении ID бота: {e}", level="error")
        return 0

def get_bot_username():
    """
    Получает username бота из кэшированной информации.

    Returns:
        str: Username бота или 'unknown_bot' в случае ошибки
    """
    try:
        bot_info = get_bot_info()
        return bot_info.get('username', 'unknown_bot') if bot_info else 'unknown_bot'
    except Exception as e:
        log_admin(f"Ошибка при получении username бота: {e}", level="error")
        return 'unknown_bot'