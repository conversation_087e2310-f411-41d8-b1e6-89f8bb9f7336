2025-07-23 14:59:26 - INFO - Database initialized successfully
2025-07-23 14:59:27 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 14:59:27 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 14:59:27 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 14:59:27 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-07-23 14:59:27 - INFO - Initializing bot_data.json with complete structure...
2025-07-23 14:59:27 - INFO - Loaded 0 group model settings
2025-07-23 14:59:27 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 14:59:27 - INFO - Initialized podcast queue with 50 workers
2025-07-23 14:59:27 - INFO - HTML Group: Module initialized
2025-07-23 14:59:27 - INFO - HTML Group: Module ready for use with random key selection!
2025-07-23 14:59:33 - INFO - Database initialized successfully
2025-07-23 14:59:33 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 14:59:33 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 14:59:33 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 14:59:33 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 14:59:33 - INFO - Initialized podcast queue with 50 workers
2025-07-23 14:59:33 - INFO - HTML Group: Module initialized
2025-07-23 14:59:33 - INFO - HTML Group: Module ready for use with random key selection!
2025-07-23 14:59:33 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-07-23 14:59:33 - INFO - Loaded 0 group model settings
2025-07-23 15:04:37 - INFO - Database initialized successfully
2025-07-23 15:04:38 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 15:04:38 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 15:04:38 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 15:04:38 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-07-23 15:04:38 - INFO - Loaded 0 group model settings
2025-07-23 15:04:38 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 15:04:38 - INFO - Initialized podcast queue with 50 workers
2025-07-23 15:04:38 - INFO - HTML Group: Module initialized
2025-07-23 15:04:38 - INFO - HTML Group: Module ready for use with random key selection!
2025-07-23 15:04:38 - WARNING - 🚀 Запуск бота через start_bot()...
2025-07-23 15:04:38 - INFO - Checking for ffmpeg...
2025-07-23 15:04:38 - INFO - ffmpeg found.
2025-07-23 15:04:38 - INFO - Initializing telegra.ph client...
2025-07-23 15:04:39 - INFO - Информация о боте успешно получена: @UseShBot (ID: **********)
2025-07-23 15:04:39 - INFO - loaded telegra.ph token from .telegraph_token
2025-07-23 15:04:39 - INFO - telegra.ph client initialized for account: UseShBot
2025-07-23 15:04:39 - INFO - telegra.ph client ready.
2025-07-23 15:04:39 - INFO - Загрузка настроек пользователей...
2025-07-23 15:04:39 - INFO - Файл настроек пользователей не найден. Будут использованы настройки по умолчанию.
2025-07-23 15:04:39 - INFO - Используются настройки пользователей по умолчанию.
2025-07-23 15:04:39 - INFO - Инициализация базы данных...
2025-07-23 15:04:39 - INFO - Database initialized successfully
2025-07-23 15:04:39 - INFO - База данных успешно инициализирована.
2025-07-23 15:04:39 - INFO - Unified background scheduler started
2025-07-23 15:04:39 - INFO - Unified background scheduler thread started (оптимизированный).
2025-07-23 15:04:39 - INFO - Cleanup scheduler disabled by configuration (AUTO_CLEANUP_ENABLED=False)
2025-07-23 15:04:39 - INFO - Bot sh is starting...
2025-07-23 15:04:39 - INFO - MAIN: Bot polling starting...
2025-07-23 15:04:44 - INFO - 🔥 Запуск разогрева соединений для быстрого отклика...
2025-07-23 15:04:44 - INFO - [GenAI] Starting connection warmup for 2 keys
2025-07-23 15:04:46 - INFO - [GenAI] Connection warmup completed: 2/2 successful
2025-07-23 15:04:46 - INFO - ✅ Разогрев завершен: 2 соединений готовы
2025-07-23 15:05:27 - CRITICAL - Цикл polling завершен или прерван
2025-07-23 15:05:27 - INFO - Stopping bot...
2025-07-23 15:05:27 - INFO - Сохранение настроек пользователей перед завершением...
2025-07-23 15:05:27 - INFO - Настройки пользователей успешно сохранены.
2025-07-23 15:05:27 - WARNING - ✅ Бот остановлен
2025-07-23 15:05:48 - INFO - Database initialized successfully
2025-07-23 15:05:49 - INFO - [PerformanceMonitor] Initialized with window_size=100, enabled=True
2025-07-23 15:05:49 - INFO - [ResourceManager] Initialized with cache_ttl=3600s, cleanup_interval=300s
2025-07-23 15:05:49 - INFO - [GenAIClientManager] Initialized with 69 API keys and performance optimizations
2025-07-23 15:05:49 - INFO - Loaded bot data: 0 admins, 0 blocked users, 0 scheduled podcasts, 0 pro users, VEO enabled: True
2025-07-23 15:05:49 - INFO - Loaded 0 group model settings
2025-07-23 15:05:49 - INFO - ThreadPoolManager initialized with max_workers=16
2025-07-23 15:05:49 - INFO - Initialized podcast queue with 50 workers
2025-07-23 15:05:49 - INFO - HTML Group: Module initialized
2025-07-23 15:05:49 - INFO - HTML Group: Module ready for use with random key selection!
2025-07-23 15:05:49 - WARNING - 🚀 Запуск бота через start_bot()...
2025-07-23 15:05:49 - INFO - Checking for ffmpeg...
2025-07-23 15:05:49 - INFO - ffmpeg found.
2025-07-23 15:05:49 - INFO - Initializing telegra.ph client...
2025-07-23 15:05:49 - INFO - Информация о боте успешно получена: @UseShBot (ID: **********)
2025-07-23 15:05:49 - INFO - loaded telegra.ph token from .telegraph_token
2025-07-23 15:05:50 - INFO - telegra.ph client initialized for account: UseShBot
2025-07-23 15:05:50 - INFO - telegra.ph client ready.
2025-07-23 15:05:50 - INFO - Загрузка настроек пользователей...
2025-07-23 15:05:50 - INFO - Файл настроек пользователей не найден. Будут использованы настройки по умолчанию.
2025-07-23 15:05:50 - INFO - Используются настройки пользователей по умолчанию.
2025-07-23 15:05:50 - INFO - Инициализация базы данных...
2025-07-23 15:05:50 - INFO - Database initialized successfully
2025-07-23 15:05:50 - INFO - База данных успешно инициализирована.
2025-07-23 15:05:50 - INFO - Unified background scheduler started
2025-07-23 15:05:50 - INFO - Unified background scheduler thread started (оптимизированный).
2025-07-23 15:05:50 - INFO - Cleanup scheduler disabled by configuration (AUTO_CLEANUP_ENABLED=False)
2025-07-23 15:05:50 - INFO - Bot sh is starting...
2025-07-23 15:05:50 - INFO - MAIN: Bot polling starting...
2025-07-23 15:05:55 - INFO - 🔥 Запуск разогрева соединений для быстрого отклика...
2025-07-23 15:05:55 - INFO - [GenAI] Starting connection warmup for 2 keys
2025-07-23 15:05:57 - INFO - [GenAI] Connection warmup completed: 2/2 successful
2025-07-23 15:05:57 - INFO - ✅ Разогрев завершен: 2 соединений готовы
